<?php

  class FileDeployConfig {

    private $projectName;
    private $projectEnvironment;
    private $localRootDir;

    private $projectFtpUsername;
    private $projectFtpPassword;
    private $projectFtpPassivemode = true;
    private $projectFtpHost;
    private $projectFtpDeployDir;
    private $projectSettings;

    private $deployConfigArray;

    private $runMode;

    private $allowDelete;

    private bool $flatMode;

    private array $itemsToSync = ["root", "gsdfw", "projects"];

    function __construct() {
      // default config settings
      $this->deployConfigArray            = [];
      $this->deployConfigArray['tempDir'] = __DIR__ . '/../../../../temp'; //put in root temp dir
      $this->deployConfigArray['colors']  = true;

      $this->runMode            = '';
      $this->allowDelete        = false;
      $this->flatMode           = false;
    }

    function setProject($project_name) {
      // todo: check if folder exists
      $this->projectName = $project_name;
    }

    function setProjectEnvironment($project_environment) {
      // todo: check if file exists
      $this->projectEnvironment = $project_environment;
    }

    /**
     * Set password
     * @param $password
     */
    function setPassword($password) {
      $this->projectFtpPassword = urlencode($password);
    }

    /**
     *  Retrieve the reversed array of files from a folder, define which files you want from a folder and the function will return
     *  an array of all the other files (which can be used to ignore those files)
     *
     * @param $directory
     * @param $include_files  array of files which should be included
     * @return array
     */
    function getIgnoreFiles($directory, $include_files) {

      $files_to_ignore = [];
      $files_and_dirs  = scandir($directory);

      foreach ($files_and_dirs as $_file_dir) {
        if ($_file_dir == '.' || $_file_dir == '..') continue;
        // add all the files to the ignore array, except the ones which should be included
        if (!in_array($_file_dir, $include_files)) {
          $files_to_ignore[] = $_file_dir;
        }
      }

      return $files_to_ignore;
    }


    /**
     * This will upload specific root directories
     */
    function addRootFolders() {
      $ignore_files = [];
      // ignore all files/folders in root
      $ignore_files[] = '/*';
      // do not ignore the files in the root
      $ignore_files[] = '!/*.*';

      // do not ignore these folders, but do ignore all their content
      $root_folders = ['archive', 'cache', 'config', 'downloads', 'logs', 'temp', 'gsdfw', 'projects'];
      foreach ($root_folders as $_root_folder) {
        $ignore_files[] = '!/' . $_root_folder . '/';
        $ignore_files[] = '/' . $_root_folder . '/*';
      }

      //do not ignore these root folders
      if (isset($this->projectSettings['additional_root_folders'])) {
        foreach ($this->projectSettings['additional_root_folders'] as $addfolder) {
          $ignore_files[] = '!' . $addfolder;
        }
      }

      //get default ignore files/folders
      $ignore_files = array_merge($ignore_files, $this->getDefaultIgnores());

      // do not ignore the .htaccess files in folders one level deep
      $ignore_files[] = '!/*/.htaccess';

      // ignore the .htaccess files in the root (can contain project specific data)
      $ignore_files[] = '/.htaccess';

      $config_key                           = 'upload_root';
      $this->deployConfigArray[$config_key] = [
        'remote'      => $this->getRemote(),
        'user'        => $this->projectFtpUsername,
        'password'    => $this->projectFtpPassword,
        'passivemode' => $this->projectFtpPassivemode,
        'local'       => $this->localRootDir,
        'test'        => false,
        'ignore'      => $ignore_files,
        'allowDelete' => ($this->allowDelete && in_array($this->allowDelete, ['all', 'root'])) ? true : false,
        'preprocess'  => false,
      ];

    }

    /**
     * This will upload the default GSDFW files
     */
    function addGsdfwDefaultFiles() {

      $gsdfw_dir = 'gsdfw/';

      $ignore_files = [];
      // ignore all files/folders in /gsdfw
      $ignore_files[] = '/*';

      // do not ignore these folders
      $gsdfw_folders = ['images', 'includes', 'modules', 'config', 'plugins', 'domain', 'projects', 'main'];
      foreach ($gsdfw_folders as $_gsdfw_folder) {
        $ignore_files[] = '!/' . $_gsdfw_folder . '/';
      }

      $ignore_files = array_merge($ignore_files, $this->getDefaultIgnores());

      $config_key                           = 'upload_gsdfw_default';
      $this->deployConfigArray[$config_key] = [
        'remote'      => $this->getRemote() . $gsdfw_dir,
        'user'        => $this->projectFtpUsername,
        'password'    => $this->projectFtpPassword,
        'passivemode' => $this->projectFtpPassivemode,
        'local'       => $this->localRootDir . $gsdfw_dir,
        'test'        => false,
        'ignore'      => $ignore_files,
        'allowDelete' => ($this->allowDelete && in_array($this->allowDelete, ['all', 'gsdfw'])) ? true : false,
        'preprocess'  => false,
      ];
    }

    function addProjectFolder() {

      $projects_dir = 'projects/';

      $ignore_files = [];
      // ignore all files/folders in /projects
      $ignore_files[] = '/*';

      // do not ignore these folders,
      $projects_folders = ['default', $this->projectName];
      // add projects folders if set in project config
      if (isset($this->projectSettings['additional_project_folders'])) {
        $projects_folders = array_merge($projects_folders, $this->projectSettings['additional_project_folders']);
      }

      //add these project folders
      foreach ($projects_folders as $_projects_folder) {
        $ignore_files[] = '!/' . $_projects_folder . '/';
      }

      // ignore all folder src: /projects/default/templates/backend/src/
      $ignore_files[] = '/default/templates/backend/src/';

      //ignore project resource folder
      $ignore_files[] = '/' . $this->projectName . '/resources/';

      //ignore project docs folder
      $ignore_files[] = '/' . $this->projectName . '/docs/';

      //ignore project docker folder
      $ignore_files[] = '/' . $this->projectName . '/docker/';

      $ignore_files = array_merge($ignore_files, $this->getDefaultIgnores());

      //ignore specific folders/files
      if (isset($this->projectSettings['folders_to_ignore'])) {
        foreach ($this->projectSettings['folders_to_ignore'] as $ignore_folder) {
          $ignore_files[] = $ignore_folder;
        }
      }

      $config_key                           = 'upload_projects_folder';
      $this->deployConfigArray[$config_key] = [
        'remote'      => $this->getRemote() . $projects_dir,
        'user'        => $this->projectFtpUsername,
        'password'    => $this->projectFtpPassword,
        'passivemode' => $this->projectFtpPassivemode,
        'local'       => $this->localRootDir . $projects_dir,
        'test'        => false,
        'ignore'      => $ignore_files,
        'allowDelete' => ($this->allowDelete && in_array($this->allowDelete, ['all', 'projects'])) ? true : false,
        'preprocess'  => false,
      ];
    }

    function setRootDeployDirectory($local_root_dir) {
      $this->localRootDir = $local_root_dir;
    }

    function getRemote() {
      // todo: check for slashes
      // should be like ftp://user:<EMAIL>/directory
      if ($this->isFlatMode()) {
        return 'ftp://' . $this->projectFtpUsername . ':' . $this->projectFtpPassword . '@' . $this->projectFtpHost . $this->projectFtpDeployDir;
      }

      //sftp needs SSH2 extension
      //return 'sftp://' . $this->projectFtpUsername . ':' . $this->projectFtpPassword . '@' . $this->projectFtpHost . $this->projectFtpDeployDir;

      return 'phpsec://' . $this->projectFtpUsername . ':' . $this->projectFtpPassword . '@' . $this->projectFtpHost . $this->projectFtpDeployDir;
    }

    function setProjectData() {
      $configFound = false;
      $configPath  = '../../../docs/' . $this->projectName . '/deploy_config.' . $this->projectEnvironment . '.php';
      if (file_exists($configPath)) {
        $configFound = true;
      }
      if (!$configFound) {
        //misschien is er een project docs folder? /docs/
        $configPath = '../../../projects/' . $this->projectName . '/docs/deploy_config.' . $this->projectEnvironment . '.php';
        if (file_exists($configPath)) {
          $configFound = true;
        }
        else { // misschien in docs/deploy/
          $configPath = '../../../projects/' . $this->projectName . '/docs/deploy/deploy_config.' . $this->projectEnvironment . '.php';
          if (file_exists($configPath)) {
            $configFound = true;
          }
        }
      }
      if (!$configFound) {
        die("Config pad niet gevonden: " . $configPath);
      }
      $project_settings = include $configPath;

      $this->projectFtpHost      = $project_settings['ftp']['host'];
      $this->projectFtpUsername  = $project_settings['ftp']['username'];
      $this->projectFtpDeployDir = $project_settings['ftp']['deploy_dir'];

      if (isset($project_settings['ftp']['password'])) {
        $this->projectFtpPassword = $project_settings['ftp']['password'];
        echo "\n! DO NOT USE PASSWORD IN CONFIG !\n\n";
      }

      if (isset($project_settings['ftp']['passivemode'])) {
        $this->projectFtpPassivemode = $project_settings['ftp']['passivemode'];
      }

      // for all additional config settings
      $this->projectSettings = $project_settings;
    }

    function setGenerateData() {
      // set fake ftp data, else ftp-deployer will through an error
      $this->projectFtpHost      = 'server17.firstfind.nl';
      $this->projectFtpUsername  = 'test';
      $this->projectFtpPassword  = 'test';
      $this->projectFtpDeployDir = '';
    }


    function getDeployConfigArray() {
      return $this->deployConfigArray;
    }

    function setLogfile($log_file_name) {
      $log_file_path = '../../../logs/' . $log_file_name . '.log';
      if ($this->getRunMode() == 'generate') {
        $log_file_path = 'generate.log';
      }
      $this->deployConfigArray['log'] = $log_file_path;
    }

    public function setRunMode($run_mode) {
      return $this->runMode = $run_mode;
    }

    public function getRunMode() {
      if (in_array($this->runMode, ['generate', 'test'])) {
        return $this->runMode;
      }

      return '';
    }

    public function setOptionalParams($params) {

      foreach ($params as $_param_name => $_param_value) {

        switch ($_param_name) {
          case 'root_only':
            $this->itemsToSync = ["root"];
            break;

          case 'gsdfw_only':
            $this->itemsToSync = ["gsdfw"];
            break;

          case 'projects_only':
            $this->itemsToSync = ["projects"];
            break;

          case 'test_mode':
            $this->setRunMode('test');
            break;

          case 'generate_mode':
            $this->setRunMode('generate');
            break;

          case 'allow_delete':
            $this->setAllowDelete($_param_value);
            break;

          case 'flatftp':
            $this->setFlatMode((bool)$_param_value);
            break;
        }
      }
    }

    public function addFolders() {

      if (in_array("root", $this->itemsToSync)) {
        $this->addRootFolders();
      }
      if (in_array("gsdfw", $this->itemsToSync)) {
        $this->addGsdfwDefaultFiles();
      }
      if (in_array("projects", $this->itemsToSync)) {
        $this->addProjectFolder();
      }

    }

    private function setAllowDelete($allow_delete) {
      if (in_array($allow_delete, ['all', 'gsdfw', 'projects', 'root'])) {
        $this->allowDelete = $allow_delete;
      }
    }

    /**
     * Standard files and folder to ignore on production
     * @return array
     */
    private function getDefaultIgnores() {
      $ignore_def_files = [];

      $ignore_def_files[] = '/.idea/';
      $ignore_def_files[] = '/.svn/';
      $ignore_def_files[] = '/.git/';
      $ignore_def_files[] = '/dev/';
      $ignore_def_files[] = '.gitignore';
      $ignore_def_files[] = 'composer.json';
      $ignore_def_files[] = 'composer.lock';
      $ignore_def_files[] = '*.md';
      $ignore_def_files[] = 'LICENSE';
      $ignore_def_files[] = 'MIT-LICENSE*';
      $ignore_def_files[] = 'GPL-LICENSE*';
      $ignore_def_files[] = 'CHANGES';
      $ignore_def_files[] = 'README';
//      $ignore_def_files[] = 'VERSION';
      $ignore_def_files[] = 'build.xml';
      $ignore_def_files[] = 'phpunit.xml';
      $ignore_def_files[] = 'phpunit.xml.dist';
      $ignore_def_files[] = 'travis.yml';
      $ignore_def_files[] = '*.log';
      $ignore_def_files[] = '.DS_Store';
      $ignore_def_files[] = '*Zone.Identifier';

      //vendor folders
      $ignore_def_files[] = '/includes/vendor/*/.svn/';
      $ignore_def_files[] = '/includes/vendor/*/*/.svn/';
      $ignore_def_files[] = '/includes/vendor/*/*/*/.svn/';
      $ignore_def_files[] = '/includes/vendor/*/.git*';
      $ignore_def_files[] = '/includes/vendor/*/*/.git*';
      $ignore_def_files[] = '/includes/vendor/*/*/*/git*';
      $ignore_def_files[] = '/includes/vendor/*/*.txt';
      $ignore_def_files[] = '/includes/vendor/*/*/*.txt';
      $ignore_def_files[] = '/includes/vendor/*/*/*/*.txt';
      $ignore_def_files[] = '/includes/vendor/*/*.pdf';
      $ignore_def_files[] = '/includes/vendor/*/*/*.pdf';
      $ignore_def_files[] = '/includes/vendor/*/*/*/*.pdf';
      $ignore_def_files[] = '/includes/vendor/*/docs/';
      $ignore_def_files[] = '/includes/vendor/*/*/docs/';
      $ignore_def_files[] = '/includes/vendor/*/*/*/docs/';
      $ignore_def_files[] = '/includes/vendor/*/doc/';
      $ignore_def_files[] = '/includes/vendor/*/*/doc/';
      $ignore_def_files[] = '/includes/vendor/*/*/*/doc/';
      $ignore_def_files[] = '/includes/vendor/*/tests/';
      $ignore_def_files[] = '/includes/vendor/*/*/tests/';
      $ignore_def_files[] = '/includes/vendor/*/*/*/tests/';
      $ignore_def_files[] = '/includes/vendor/*/test/';
      $ignore_def_files[] = '/includes/vendor/*/*/test/';
      $ignore_def_files[] = '/includes/vendor/*/*/*/test/';
      $ignore_def_files[] = '/includes/vendor/*/unitTests/';
      $ignore_def_files[] = '/includes/vendor/*/*/unitTests/';
      $ignore_def_files[] = '/includes/vendor/*/*/*/unitTests/';
      $ignore_def_files[] = '/includes/vendor/*/examples/';
      $ignore_def_files[] = '/includes/vendor/*/*/examples/';
      $ignore_def_files[] = '/includes/vendor/*/*/*/examples/';
      $ignore_def_files[] = '/includes/vendor/*/example/';
      $ignore_def_files[] = '/includes/vendor/*/*/example/';
      $ignore_def_files[] = '/includes/vendor/*/*/*/example/';
      $ignore_def_files[] = '/includes/vendor/*/samples/';
      $ignore_def_files[] = '/includes/vendor/*/*/samples/';
      $ignore_def_files[] = '/includes/vendor/*/*/*/samples/';

      //TESTS:
      //      $ignore_files[] = 'test.svn'; //negeer map/bestand met naam test.svn in elke folder
      //      $ignore_files[] = '.svn'; //negeer map/bestand met naam .svn in elke folder
      //      $ignore_files[] = '*.svn';  //negeer bestandnaam of foldernaam [elke karakter of geen karakter].svn ergens in de naam in elke folder
      //      $ignore_files[] = '*.svn*'; //negeer bestandnaam of foldernaam [elke karakter of geen karakter].svn[elke karakter of geen karakter] ergens in de naam in elke folder
      //      $ignore_files[] = '/.svn'; //negeer map/bestand .svn in de root
      //      $ignore_files[] = '.svn/*'; //werkt niet
      //      $ignore_files[] = '/.svn/'; //werkt niet
      //      $ignore_files[] = '*/.svn/'; //werkt niet
      //      $ignore_files[] = '*/.svn/*'; //werkt niet

      return $ignore_def_files;
    }

    /**
     * @return bool
     */
    public function isFlatMode(): bool {
      return $this->flatMode;
    }

    /**
     * @param bool $flatMode
     */
    public function setFlatMode(bool $flatMode): void {
      $this->flatMode = $flatMode;
    }

  }