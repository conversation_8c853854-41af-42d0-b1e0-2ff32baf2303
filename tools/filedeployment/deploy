#!/usr/bin/env php
<?php
  chdir(__DIR__);

  // if we want to use composer packages
  use Gsd\Updater\GsdfwUpdater;


  /*********************
   * CONVERT OPTIONAL PARAMETERS FROM CLI
   * Set before configure load.
   *********************/

  $cli_params = array();
  // extra parameters for deployment
  if(isset($_SERVER['argv']) && is_array($_SERVER['argv']) && count($_SERVER['argv']) > 1) {
    foreach ($_SERVER['argv'] as $_cli_param) {

      if($_cli_param == '-projects') { //only send projects
        $cli_params['projects_only'] = true;
        continue;
      }
      if($_cli_param == '-gsdfw') { //only send projects
        $cli_params['gsdfw_only'] = true;
        continue;
      }
      if($_cli_param == '-root') { //only send projects
        $cli_params['root_only'] = true;
        continue;
      }
      if($_cli_param == '-dryrun') { //dryrun, no files are send to server (voorheen ook -t of -test)
        $cli_params['test_mode'] = true;
        continue;
      }
      if($_cli_param == '--generate') {
        $cli_params['generate_mode'] = true;
        continue;
      }
      if($_cli_param == '-flatftp') {
        $cli_params['flatftp'] = true;
        continue;
      }
      if(basename($_cli_param) == 'deploy') {
        continue;
      }
      else if(substr($_cli_param, 0, 3) == '-p=') { //set project_name
        $project_name = substr($_cli_param, 3);
        continue;
      }
      if(substr($_cli_param, 0, 3) == '-e=') { //set project_environment
        $project_environment = substr($_cli_param, 3);
        continue;
      }
      if(substr($_cli_param, 0, 5) == '-del=') { //delete removed files (dryrun first): 'all', 'gsdfw', 'projects', 'root'
        $cli_params['allow_delete'] = substr($_cli_param, 5);
        continue;
      }
      if(substr($_cli_param, 0, 4) == '-pw=') { //password
        $password = substr($_cli_param, 4);
        continue;
      }

      throw new Exception('Unknown parameter.');
    }
  }

  require_once("../../../gsdfw/includes/classes/Config.php");
  require_once("../../../gsdfw/includes/classes/Context.php");

  require_once('../../../config/configure.inc.php');
  require_once('../../includes/vendor/autoload.php');
  require_once('../../includes/classes/helpers/RequestHelper.php');

  if(!defined("GSD_EMPLOYEE_USERNAME")) {
    echo "GSD_EMPLOYEE_USERNAME niet gefineerd in /config/configure.inc.php. Zie /config/configure_example.inc.php voor een voorbeeld.\n";
    exit(1);
  }

  ini_set('display_errors', true);
  error_reporting(E_ALL);

  date_default_timezone_set('Europe/Amsterdam');

  /*********************
   * Ask for project name and environment in command line
   *********************/

  if(!isset($project_name) || $project_name=="") {
    // see: https://github.com/dg/ftp-deployment/issues/75
    echo "Enter the project name you wish to deploy: ";
    $project_name = stream_get_line(STDIN, 1024, PHP_EOL);
  }

  if(!isset($project_environment) || $project_environment=="") {
    echo "Enter the project environment (eg live/staging): ";
    $project_environment = stream_get_line(STDIN, 1024, PHP_EOL);
  }

  if(!isset($password) || $password=="") {
    echo "Enter FTP password: ";
    $password = stream_get_line(STDIN, 1024, PHP_EOL);
  }

  /*********************
   * FILE DEPLOYMENT
   *********************/

  require __DIR__ . '/filedeployment/FileDeployConfig.php';
  require __DIR__ . '/filedeployment/FileDeploy.php';

  // first load the config file for deploying the files
  $fileDeployConfig = new FileDeployConfig();
  $fileDeployConfig->setProject($project_name);
  $fileDeployConfig->setProjectEnvironment($project_environment);
  $fileDeployConfig->setOptionalParams($cli_params);
  $fileDeployConfig->setLogfile(date('YmdHis') . $project_name);
  if($fileDeployConfig->getRunMode() == 'generate') {
    $fileDeployConfig->setGenerateData();
  }
  else {
    $fileDeployConfig->setProjectData();
  }
  if(isset($password)) { //user password from commandline
    $fileDeployConfig->setPassword($password);
  }
  
  $fileDeployConfig->setRootDeployDirectory(__DIR__ . '/../../../');

  $fileDeployConfig->addFolders();


  // load config file in the deploy script and deploy the files
  $fileDeploy = new FileDeploy($fileDeployConfig);
  $fileDeploy->deployFiles();

  /*****************************************
   * WRITE VERSION TO VERSION DATABASE
   *****************************************/

  if(!isset($cli_params['test_mode']) || $cli_params['test_mode']==false) {
    $releaseDatabaseSuccess = false;
    try {
      $log = [];
      $log["project"] = $project_name;
      $log["environment"] = $project_environment;
      $log["gsd_username"] = GSD_EMPLOYEE_USERNAME;

      $domain = "https://beheer.gsd.nl";
      //    if (DEVELOPMENT) {
      //      $domain = "http://beheer.gsd.nl.gsd.localhost";
      //    }
      $result = RequestHelper::sendPostToUrl($log, $domain . "/nl/external?action=release");
      if($result=="ok") {
        $releaseDatabaseSuccess = true;
      }
      else {
        $fileDeploy->logger->log("Release database not updated: unknown error. Check error mailbox.", "red");
      }

    }
    catch (Exception $e) {
      $fileDeploy->logger->log("Release database not updated: ", "red");
      $fileDeploy->logger->log($e->getMessage(), "red");
      exit(1);
    }

    if ($releaseDatabaseSuccess) {
      $fileDeploy->logger->log("Release database updated at " . date('d-m-Y H:i:s'), "lime");
    }
  }

  echo "\n";


