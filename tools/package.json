{"name": "gsdfw", "description": "GSD Framework node modules", "version": "1.0.1", "private": true, "author": {"name": "<PERSON> - Geenen Software Development", "email": "<EMAIL>", "url": "https://www.gsd.nl"}, "license": "UNLICENSED", "scripts": {"build-prod": "cross-env NODE_ENV=production webpack", "build-dev": "cross-env NODE_ENV=development webpack", "watch": "cross-env NODE_ENV=development webpack --watch", "backend-build-production": "cross-env NODE_ENV=production PROJECT=$PROJECT webpack --config ../resources/default/backend/webpack.config.js", "backend-build-development": "cross-env NODE_ENV=development PROJECT=$PROJECT webpack --config ../resources/default/backend/webpack.config.js", "backend-watch-development": "cross-env NODE_ENV=development PROJECT=$PROJECT webpack --config ../resources/default/backend/webpack.config.js  --watch", "backend2-build-libraries-production": "cross-env NODE_ENV=production PROJECT=$PROJECT webpack --config ../resources/default/backend2/webpack.libraries.config.js", "backend2-build-libraries-development": "cross-env NODE_ENV=development PROJECT=$PROJECT webpack --config ../resources/default/backend2/webpack.libraries.config.js", "backend2-build-production": "cross-env NODE_ENV=production PROJECT=$PROJECT webpack --config ../resources/default/backend2/webpack.config.js", "backend2-build-development": "cross-env NODE_ENV=development PROJECT=$PROJECT webpack --config ../resources/default/backend2/webpack.config.js", "backend2-watch-development": "cross-env NODE_ENV=development PROJECT=$PROJECT webpack --config ../resources/default/backend2/webpack.config.js  --watch", "rde-frontend-build-production": "cross-env NODE_ENV=production webpack --config ../../projects/rde/resources/frontend/webpack.config.js", "rde-frontend-build-development": "cross-env NODE_ENV=development webpack --config ../../projects/rde/resources/frontend/webpack.config.js", "rde-frontend-watch-development": "cross-env NODE_ENV=development webpack --config ../../projects/rde/resources/frontend/webpack.config.js  --watch", "heblad-pingpongtafel-build-production": "cross-env NODE_ENV=production webpack --config ../../projects/heblad/resources/pingpongtafel/webpack.config.js", "heblad-pingpongtafel-build-development": "cross-env NODE_ENV=development webpack --config ../../projects/heblad/resources/pingpongtafel/webpack.config.js", "heblad-pingpongtafel-watch-development": "cross-env NODE_ENV=development webpack --config ../../projects/heblad/resources/pingpongtafel/webpack.config.js --watch", "heblad-frontend-build-production": "cross-env NODE_ENV=production webpack --config ../../projects/heblad/resources/frontend/webpack.config.js", "heblad-frontend-build-development": "cross-env NODE_ENV=development webpack --config ../../projects/heblad/resources/frontend/webpack.config.js", "heblad-frontend-watch-development": "cross-env NODE_ENV=development webpack --config ../../projects/heblad/resources/frontend/webpack.config.js --watch", "heblad-frontend-build-libraries-production": "cross-env NODE_ENV=production webpack --config ../../projects/heblad/resources/frontend/webpack.libraries.config.js", "heblad-frontend-build-libraries-development": "cross-env NODE_ENV=development webpack --config ../../projects/heblad/resources/frontend/webpack.libraries.config.js", "heblad-frontend-watch-libraries-development": "cross-env NODE_ENV=development webpack --config ../../projects/heblad/resources/frontend/webpack.libraries.config.js --watch", "heblad-backend-build-production": "cross-env NODE_ENV=production webpack --config ../../projects/heblad/resources/backend/webpack.config.js", "heblad-backend-build-development": "cross-env NODE_ENV=development webpack --config ../../projects/heblad/resources/backend/webpack.config.js", "heblad-backend-watch-development": "cross-env NODE_ENV=development webpack --config ../../projects/heblad/resources/backend/webpack.config.js --watch", "heblad-backend-build-libraries-production": "cross-env NODE_ENV=production webpack --config ../../projects/heblad/resources/backend/webpack.libraries.config.js", "heblad-backend-build-libraries-development": "cross-env NODE_ENV=development webpack --config ../../projects/heblad/resources/backend/webpack.libraries.config.js", "heblad-backend-watch-libraries-development": "cross-env NODE_ENV=development webpack --config ../../projects/heblad/resources/backend/webpack.libraries.config.js --watch", "jam-build-backend-production": "cross-env NODE_ENV=production webpack --config ../../docs/default/webpack.config.js", "jam-build-backend-development": "cross-env NODE_ENV=development webpack --config ../../docs/default/webpack.config.js", "jam-build-frontend-production": "cross-env NODE_ENV=production webpack --config ../../resources/frontend/webpack.config.js", "jam-build-frontend-development": "cross-env NODE_ENV=development webpack --config ../../resources/frontend/webpack.config.js", "jam-watch-frontend-development": "cross-env NODE_ENV=development webpack --config ../../resources/frontend/webpack.config.js --watch", "vdhoutmetaal-build-backend-production": "cross-env NODE_ENV=production webpack --config ../../resources/vdhoutmetaal/urenapp/webpack.config.js", "vdhoutmetaal-build-backend-development": "cross-env NODE_ENV=development webpack --config ../../resources/vdhoutmetaal/urenapp/webpack.config.js", "vdhoutmetaal-watch-backend-development": "cross-env NODE_ENV=development webpack --config ../../resources/vdhoutmetaal/urenapp/webpack.config.js --watch", "olifanttrekhaken-prod": "cross-env NODE_ENV=production webpack --config ../../resources/olifanttrekhaken/frontend/webpack.config.js", "olifanttrekhaken-dev": "cross-env NODE_ENV=development webpack --config ../../resources/olifanttrekhaken/frontend/webpack.config.js", "olifanttrekhaken-watch": "cross-env NODE_ENV=development webpack --config ../../resources/olifanttrekhaken/frontend/webpack.config.js --watch", "jari-frontend-build-production": "cross-env NODE_ENV=production webpack --config ../../projects/jari/resources/frontend/webpack.config.js", "jari-frontend-build-development": "cross-env NODE_ENV=development webpack --config ../../projects/jari/resources/frontend/webpack.config.js", "jari-frontend-watch-development": "cross-env NODE_ENV=development webpack --config ../../projects/jari/resources/frontend/webpack.config.js --watch"}, "dependencies": {"autoprefixer": "^10.4.13", "font-awesome": "^4.7.0", "jquery-migrate": "^3.4.1", "jquery-validation": "^1.19.5", "postcss": "^8.4.24", "postcss-scss": "^4.0.6", "simple-line-icons": "^2.5.5", "simplelightbox": "2.10", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17"}, "devDependencies": {"babel-core": "^6.26.3", "babel-loader": "^9.1.2", "babel-preset-env": "^1.7.0", "clean-webpack-plugin": "^4.0.0", "cross-env": "^7.0.3", "css-loader": "^6.7.3", "css-minimizer-webpack-plugin": "^4.2.2", "devbridge-autocomplete": "^1.4.11", "exports-loader": "^4.0.0", "flatpickr": "^4.6.13", "html-webpack-plugin": "^5.5.0", "image-webpack-loader": "^8.1.0", "jquery": "^3.6.3", "jquery.easing": "^1.4.1", "mini-css-extract-plugin": "^2.7.2", "postcss-import": "^15.1.0", "postcss-loader": "^7.0.2", "qtip2": "^3.0.3", "sass": "^1.58.3", "sass-loader": "^13.2.0", "style-loader": "^3.3.1", "sweetalert2": "^7.33.1", "tablednd": "^1.0.5", "terser-webpack-plugin": "^5.3.6", "webpack": "^5.75.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.11.1", "webpack-md5-hash": "^0.0.6", "webpack-require-http": "^0.4.3"}}