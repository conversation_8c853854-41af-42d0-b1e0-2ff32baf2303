<?php

  /**
   * Genereert een PDF bestelbevestiging
   * Class OrderPdf
   */
  class OrderPdf extends GSDPDF {

    private $templatetype = 'digi'; //gebruik briefpapier bij digi
    private $invoiceIds = []; //the invoiceids to show
    private $ids_encrypted = false; //use md5 encryption
    private $temp_invoicenr = ''; //show different invoicenumber
    private $filename = ''; //output filename, is out generated
    private $redirect = false; //redirect directly to file

    public function __construct() {
      parent::__construct();
    }

    /**
     * Uses briefpapier digi/standard
     * @param $value
     */
    public function setTemplatetype($value) {
      $this->templatetype = $value;
    }

    /**
     * @param int $invoiceid
     */
    public function addInvoiceid($invoiceid) {
      $this->invoiceIds[$invoiceid] = $invoiceid;
    }

    /**
     * @param [] $invoiceids
     */
    public function addInvoiceids($invoiceids) {
      $this->invoiceIds = array_merge($this->invoiceIds, $invoiceids);
    }

    public function setIdsEncrypted($value) {
      $this->ids_encrypted = $value;
    }

    /**
     * Temporary invoicenumber to use
     * @param $value
     */
    public function setTempInvoicenr($value) {
      $this->temp_invoicenr = $value;
    }

    /**
     * Redirect after building
     * @param $value
     */
    public function setRedirect($value) {
      $this->redirect = $value;
    }

    /**
     * Get output filename
     * @return string
     */
    public function getFilename() {
      return $this->filename;
    }


    public function generatePdf() {

      \FileHelper::cleanup(DIR_TEMP);
      __autoloader("Invoice");  //ivm constants

      $this->AliasNbPages();
      $this->hasTemplate = false;

      $invoices = [];
      foreach ($this->invoiceIds as $id) {
        $invoice = false;
        if ($this->ids_encrypted) {
          $invoice = Invoice::getInvoiceAndProductsByMd5($id);
        }
        else {
          $invoice = Invoice::getInvoiceAndProductsById($id);
        }
        if (!$invoice) {
          die("Factuur niet gevonden. Bestelling/factuur is verwijderd?");
        }
        $invoices[] = $invoice;
      }

      foreach ($invoices as $invoice) {

        $pageno = 1;
        $pagecount = 1;

        $order = Orders::find_by_id($invoice->order_id);
        $products = $invoice->invoice_products;

        $from = $invoice->getFromUserData();
        if ($invoice->status == Invoice::INVOICE_STATUS_NEW) { //altijd uit database
          $from = User::getUserWithOrganById($invoice->from_user_id);
        }

        //$from->organisation->template = 'template.pdf';
        $hasdiscount = $invoice->hasDiscount();


        $to['user'] = $invoice->getUserData(); //no shipping_address
        $language = $to['user']->organisation->language;

        Trans::loadMainLanguagefile($language);
        Trans::loadLanguagefiles('basket', null, $language);
        Trans::loadLanguagefiles('user', null, $language);
        Trans::loadLanguagefiles('pdf', null, $language);

        $w = [0, 110, 13, 11, 17, 15, 20, 20];
        $header = ['', __('Producten'), __('Aant.'), __('BTW'), __('Basisprijs'), __('Korting'), __('Stukprijs'), __('Totaal')];
        if (Config::isTrue('PDF_SHOW_PRODUCTCODE')) {
          $w = [20, 90, 11, 9, 17, 15, 20, 20];
          $header = [__('Code'), __('Omschrijving'), __('Aant.'), __('BTW'), __('Basisprijs'), __('Korting'), __('Stukprijs'), __('Totaal')];
        }
        $this->SetWidths($w);
        $this->setHeaders($header);


        if ($order) {
          $order_userdata = $order->getUserData();
          if ($order_userdata['user']->id == $to['user']->id) { //betreft hier een factuur waaraan ook de order is gekoppeld. Dus verzendadres tonen.
            $to['shippingaddress'] = $order_userdata['shippingaddress'];
          }
        }

        $this->from = $from;

        $this->filename = "Order_" . StringHelper::slugify($order->getOrderNr()) . ".pdf";

        if ($this->templatetype == 'digi' && $from->organisation->hasTemplate()) {
          $pagecount = $this->setSourceFile($from->organisation->getTemplatePath());
          $tplidx = $this->importPage(1);
          $this->setTplidx($tplidx);
          $this->hasTemplate = true;
        }

        $this->AddPage();

        if ($this->templatetype == 'digi' && $this->hasTemplate) {
          $this->useTemplate($this->tplidx, 0, 0);
        }

        $companyname = $to['user']->organisation->name;
        $vat_number = $to['user']->organisation->vat_number;
        $name = $to['user']->getNaam();
        if ($companyname != "") {
          if (!Config::isdefined("INVOICE_SHOW_COMPANY_ATTN") || Config::get("INVOICE_SHOW_COMPANY_ATTN") === true) {
            if ($to['user']->sex == "M") {
              $name = "T.a.v. Dhr. " . $name;
            }
            elseif ($to['user']->sex == "V") {
              $name = "T.a.v. Mvr. " . $name;
            }
          }
          else {
            if (!Config::isdefined("INVOICE_SHOW_PERSONAL_SALUTATION") || Config::isTrue("INVOICE_SHOW_COMPANY_ATTN")) {
              if ($to['user']->sex == "M") {
                $name = "Dhr. " . $to['user']->lastname;
              }
              elseif ($to['user']->sex == "V") {
                $name = "Mvr. " . $to['user']->lastname;
              }
            }
            else {
              $name = "";
            }
          }
        }

        if (isset($to['user'])) {
          if (isset($to['user']) && $to['user']->organisation->invoice_equal == 1) {
            $address = $to['user']->organisation->address . " " . $to['user']->organisation->number;
            $zip = $to['user']->organisation->zip;
            $city = $to['user']->organisation->city;
            $extension = $to['user']->organisation->extension;
            $country = $to['user']->organisation->getCountry();
          }
          else {
            $address = $to['user']->organisation->invoice_address . " " . $to['user']->organisation->invoice_number;
            $zip = $to['user']->organisation->invoice_zip;
            $city = $to['user']->organisation->invoice_city;
            $extension = $to['user']->organisation->invoice_extension;
            $country = $to['user']->organisation->getInvoiceCountry();
          }
        }

        if (isset($to['shippingaddress']->address)) {
          $send_contactname = $to['shippingaddress']->contactname;
          $send_address = $to['shippingaddress']->address . " " . $to['shippingaddress']->number;
          $send_zip = $to['shippingaddress']->zip;
          $send_city = $to['shippingaddress']->city;
          $send_extension = $to['shippingaddress']->extension;
          $send_country = $to['shippingaddress']->getCountry();
        }

        $this->SetFont($this->fontname, 'B', 9);

        $this->setY(47);
        if (Config::isTrue("INVOICE_CUSTOMER_INFO_ALIGN_LEFT")) {
          if ($hasdiscount) {
            $this->SetLeftMargin(6);
          }
          else {
            $this->SetLeftMargin(17);
          }
        }
        else {
          $this->SetLeftMargin(24);
        }

        $this->Cell(92, 4, __('Factuuradres') . ':');
        $this->Cell(90, 4, __('Afleveradres') . ':');
        $this->Ln();

        $this->SetFont($this->fontname, '', 10);
        $this->Cell(92, 4, $companyname);
        $this->Cell(90, 4, $companyname);
        $this->Ln();

        if ($name != "") {
          $this->Cell(92, 4, $name);
        }
        $this->Cell(90, 4, $send_contactname);
        if ($name != "") {
          $this->Ln();
        }

        $this->Cell(92, 4, $address);
        $this->Cell(90, 4, $send_address);
        $this->Ln();

        $this->Cell(92, 4, $zip . " " . $city);
        $this->Cell(90, 4, $send_zip . " " . $send_city);
        $this->Ln();

        if ($extension) {
          $this->Cell(92, 4, $extension);
          $this->Cell(90, 4, $send_extension);
          $this->Ln();
        }

        $this->Cell(92, 4, $country);
        $this->Cell(90, 4, $send_country);
        $this->Ln();
        $this->Ln();
        $this->Ln();

        if ($hasdiscount) {
          $this->SetLeftMargin(6);
        }
        else {
          $this->SetLeftMargin(17);
        }

        if ($hasdiscount) {
          $this->setX(6);
        }
        else {
          $this->setX(17);
        }

        $this->Ln();
        $this->SetFont($this->fontname, 'B', 9);
        $this->Cell(32, 5, __('ORDERBEVESTIGING'));
        $this->Ln();
        $this->Ln();

        $this->SetFont($this->fontname, 'B');
        $this->Cell(32, 5, __('Bestelnr.') . ':');
        $this->SetFont($this->fontname, '');
        $this->Cell(30, 5, $order->getOrderNr());

        $this->SetFont($this->fontname, 'B');
        $this->Cell(32, 5, __('Besteldatum') . ': ');
        $this->SetFont($this->fontname, '');
        $invoicedate = $order->getOrderdate();
        if ($invoicedate == "") {
          $invoicedate = $order->getInsertTS("d-m-Y");
        }
        $this->Cell(45, 5, $invoicedate);

        $this->Ln();

        //   $this->setX(8);
        $this->SetFont($this->fontname, 'B');
        $this->Cell(32, 5, __('Debiteurnr.') . ':');
        $this->SetFont($this->fontname, '');
        $this->Cell(30, 5, escapeForPDF($to['user']->organisation->getCustNr()));
        if ($vat_number != "") {
          if ($invoice->type == 'credit') {
            $this->Cell(75, 5, '');
          }
          else {
            $this->SetFont($this->fontname, 'B');
            $this->Cell(32, 5, __('Uw BTW-nr.') . ': ');
            $this->SetFont($this->fontname, '');
            $this->Cell(45, 5, $vat_number);
            $this->Ln();
          }
        }
        if ($invoice->type == 'invoice') {
          $this->SetFont($this->fontname, 'B');
          $this->Cell(32, 5, __('Betaalmethode') . ':');
          $this->SetFont($this->fontname, '');
          $this->Cell(45, 5, ($invoice->paymentmethod != "") ? Invoice::$invoicemethods[$invoice->paymentmethod] : 'Overmaken op rekening');
          $this->Ln();
        }

        if ($invoice->betreft != "") {
          $this->SetFont($this->fontname, 'B');
          $this->Cell(32, 5, __('Betreft') . ':');
          $this->SetFont($this->fontname, '');
          $this->MultiCell(90, 5, $invoice->betreft);
        }
        if ($order && $order->reference != "") {
          $this->SetFont($this->fontname, 'B');
          $this->Cell(32, 5, __('Uw referentie') . ':');
          $this->SetFont($this->fontname, '');
          $this->Cell(90, 5, $order->reference);
          $this->Ln();
        }

        $this->SetTextColor(0, 0, 0);
        $this->SetFont($this->fontname, '');
        $this->SetY($this->getY());

        $this->Ln();

        $this->setX(170);
        $this->Cell(30, 5, __('Pagina') . ' ' . $pageno . '/{nb_gsd}', 0, 0, 'R');
        $this->Ln();


        //$this->SetFillColor(190,214,77); GREEN
        $this->SetFillColor(221, 221, 221);
        $this->SetDrawColor(88, 88, 88);
        $this->SetLineWidth(.2);
        $this->SetFont($this->fontname, 'B', 9);


        $hasdatum = false;
        foreach ($products as $key => $product) {
          if ($product->type == 3) {
            $hasdatum = true;
            break;
          }
        }

        //Koptekst
        $datumwidth = 17;
        if ($hasdatum) {
          $this->Cell($datumwidth, 5, __("Datum"), 1, 0, 'L', 1);
        }
        if (Config::isTrue('PDF_SHOW_PRODUCTCODE')) {
          $this->Cell($w[0], 5, $header[0], 1, 0, 'L', 1);
        }
        $this->Cell($w[1], 5, $header[1], 1, 0, 'L', 1);
        $this->Cell($w[2], 5, $header[2], 1, 0, 'R', 1);
        $this->Cell($w[3], 5, $header[3], 1, 0, 'R', 1);
        if ($hasdiscount) {
          $this->Cell($w[4], 5, $header[4], 1, 0, 'R', 1);
          $this->Cell($w[5], 5, $header[5], 1, 0, 'R', 1);
        }
        $this->Cell($w[6], 5, $header[6], 1, 0, 'R', 1);
        $this->Cell($w[7], 5, $header[7], 1, 0, 'R', 1);

        $this->Ln();
        //Herstel van kleuren en lettertype
        $this->SetFillColor(221, 221, 221);
        $this->SetTextColor(0);
        $this->SetFont($this->fontname, '', 9);


        $tel = 0;
        $telfornext = 30;
        $transtotaal = 0;
        $subtotaal = 0;

        $border = 'LRBT';

        $use_multiline = true;
        foreach ($products as $key => $product) {
          if ($tel > $telfornext) {
            $this->addNewPage($transtotaal, $hasdiscount);
            $tel = 0;
            $telfornext = 43;
          }
          $tel++;

          if ($hasdatum && $product->type != 3) {
            $this->Cell($datumwidth, 5, "", $border, 0, 'R');
          }

          if ($product->type == 2) {
            if (Config::isTrue('PDF_SHOW_PRODUCTCODE')) {
              $this->Cell($w[0], 5, $product->code, $border, 0, '', 0);
            }
            $this->Cell($w[1], 5, $product->description, $border, 0, '', 0);
            $this->Cell($w[2], 5, '', $border, 0, 'R', 0);
            $this->Cell($w[3], 5, '', $border, 0, 'R', 0);
            if ($hasdiscount) {
              $this->Cell($w[4], 5, '', $border, 0, 'R', 0);
              $this->Cell($w[5], 5, '', $border, 0, 'R', 0);
            }
            $this->Cell($w[6], 5, __("Subtotaal"), $border, 0, 'R');
            $this->SetFont('', 'B');
            $this->Cell($w[7], 5, '€ ' . getLocalePrice($subtotaal, ',', '.'), $border, 0, 'R', 0);
            $this->SetFont('', '');
            $subtotaal = 0;
            $this->Ln();
          }
          elseif ($product->type == 3) {
            $this->Cell($datumwidth, 5, $product->getDescriptiondate(), $border, 0, 'L');
            if (Config::isTrue('PDF_SHOW_PRODUCTCODE')) {
              $this->Cell($w[0], 5, $product->code, $border, 0, '', 0);
            }
            $this->Cell($w[1], 5, $product->description, $border, 0, '', 0);
            $this->Cell($w[2], 5, '', $border, 0, 'R', 0);
            $this->Cell($w[3], 5, '', $border, 0, 'R', 0);
            if ($hasdiscount) {
              $this->Cell($w[4], 5, '', $border, 0, 'R', 0);
              $this->Cell($w[5], 5, '', $border, 0, 'R', 0);
            }
            $this->Cell($w[6], 5, '', $border, 0, 'R', 0);
            $this->Cell($w[7], 5, '', $border, 0, 'R', 0);
            $this->Ln();
          }
          else {
            $subtotaal += $product->total;
            $transtotaal += $product->total;

            if ($use_multiline) {
              $args = [];
              $this->aligns[0] = 'L';
              $this->aligns[1] = 'L';
              $this->aligns[2] = 'R';
              $this->aligns[3] = 'R';
              $this->aligns[4] = 'R';
              $this->aligns[5] = 'R';
              $this->aligns[6] = 'R';
              $this->aligns[7] = 'R';

              if (Config::isTrue('PDF_SHOW_PRODUCTCODE')) {
                $args[0] = $product->code;
              }
              $args[1] = $product->description;

              $prstuks = $product->size != 0 ? $product->size : '';
              if (substr($prstuks, strpos($prstuks, '.')) == '00') {
                $prstuks = substr($prstuks, 0, strpos($prstuks, '.'));
              }
              $args[2] = $prstuks;
              $args[3] = ($product->vattype != "" ? $product->vattype : '0') . '%';

              if ($hasdiscount) {
                $baseprice = "";
                if ($product->baseprice > 0) {
                  $baseprice = '€ ' . getLocalePrice($product->baseprice, ',', '.');
                }
                $args[4] = $baseprice;

                $discount = "";
                if ($product->baseprice > 0) {
                  $discount = $product->discount . '%';
                }
                $args[5] = $discount;
              }
              $args[6] = $product->pieceprice != 0 ? '€ ' . getLocalePrice($product->pieceprice, ',', '.') : '';
              $args[7] = '€ ' . getLocalePrice($product->total, ',', '.');

              $row_height = $this->Row($args, 5);

              $tel += ($row_height / 4) - 1;
            }
            else {
              $this->Cell($w[0], 5, $product->code, $border, 0, 'L', 0);
              $this->Cell($w[1], 5, $product->description, $border, 0, 'L', 0);

              $prstuks = $product->size != 0 ? $product->size : '';
              if (substr($prstuks, strpos($prstuks, '.')) == '00') {
                $prstuks = substr($prstuks, 0, strpos($prstuks, '.'));
              }
              $this->Cell($w[2], 5, $prstuks, $border, 0, 'R', 0);
              $this->Cell($w[3], 5, ($product->vattype != "" ? $product->vattype : '0') . '%', $border, 0, 'R', 0);

              if ($hasdiscount) {
                $baseprice = "";
                if ($product->baseprice > 0) {
                  $baseprice = '€ ' . getLocalePrice($product->baseprice, ',', '.');
                }
                $this->Cell($w[4], 5, $baseprice, $border, 0, 'R', 0);

                $discount = "";
                if ($product->baseprice > 0) {
                  $discount = $product->discount . '%';
                }
                $this->Cell($w[5], 5, $discount, $border, 0, 'R', 0);
              }
              $this->Cell($w[6], 5, $product->pieceprice != 0 ? '€ ' . getLocalePrice($product->pieceprice, ',', '.') : '', $border, 0, 'R', 0);
              $this->Cell($w[7], 5, '€ ' . getLocalePrice($product->total, ',', '.'), $border, 0, 'R', 0);
            }
          }

          if (!$use_multiline && $product->type != "")
            $this->Ln();

        }

        $this->SetFont($this->fontname, '', 9);

        $width = $w[0] + $w[1] + $w[2] + $w[3] + $w[6];
        //vebreden vanwege datum
        if ($hasdatum) {
          $width += $datumwidth;
        }
        if ($hasdiscount) {
          $width += $w[4] + $w[5];
        }
        $this->Cell($width + $w[7], 0.5, '', ''); //double line
        $this->Ln();

        $this->Cell($width, 5, __("Subtotaal excl. BTW"), '', 0, 'R');
        $this->Cell($w[7], 5, '€ ' . getLocalePrice($invoice->total_excl_prods, ',', '.'), '', 0, 'R', 0);
        $this->Ln();

        $defaultvat = $invoice->determineVat();
        $defaultvatcalc = $defaultvat / 100;

        $has_xtra_costs = 0;
        //Korting betalingswijze
        if ($invoice->paymentdiscount != "" && $invoice->paymentdiscount != null && $invoice->paymentdiscount != 0) {
          $has_xtra_costs += $defaultvatcalc * $invoice->paymentdiscount;
          $this->Cell($width, 5, $invoice->paymentmethod_desc, '', 0, 'R');
          $this->Cell($w[7], 5, '€ ' . getLocalePrice($invoice->paymentdiscount, ',', '.'), '', 0, 'R', 0);
          $this->Ln();
        }

        //order costs
        if ($invoice->handle_costs != "" && $invoice->handle_costs != null && $invoice->handle_costs != 0) {
          $has_xtra_costs += $defaultvatcalc * $invoice->handle_costs;
          $this->Cell($width, 5, __('Verwerkingskosten (excl.)'), '', 0, 'R');
          $this->Cell($w[7], 5, '€ ' . getLocalePrice($invoice->handle_costs, ',', '.'), '', 0, 'R', 0);
          $this->Ln();
        }

        //shipping costs
        if ($invoice->shipping != "" && $invoice->shipping != 0 && $invoice->shipping != null) {
          $has_xtra_costs += $defaultvatcalc * $invoice->shipping;
          $this->Cell($width, 5, $invoice->shipping_desc != "" ? $invoice->shipping_desc : __('Verzendkosten'), '', 0, 'R');
          $this->Cell($w[7], 5, '€ ' . getLocalePrice($invoice->shipping, ',', '.'), '', 0, 'R', 0);
          $this->Ln();
        }

        if ($invoice->credits != null && $invoice->credits != 0) {
          $has_xtra_costs += -$defaultvatcalc * $invoice->credits;
          $this->Cell($width, 5, __('Credit tegoed opgenomen'), '', 0, 'R');
          $this->Cell($w[7], 5, '€ -' . getLocalePrice($invoice->credits, ',', '.'), '', 0, 'R', 0);
          $this->Ln();
        }

        //subtotaal, kortingen etc verwerkt
        if ($has_xtra_costs != 0) {
          $this->Cell($width, 0.5, '', '');
          $this->Cell($w[7], 0.5, '', 'T');
          $this->Ln();
          $this->Cell($width, 5, __("Subtotaal excl. BTW"), '', 0, 'R');
          $this->Cell($w[7], 5, '€ ' . getLocalePrice($invoice->total_excl, ',', '.'), '', 0, 'R', 0);
          $this->Ln();
        }

        $vats = $invoice->getVatSplit(true);
        foreach (Invoice::getVattypes() as $lvat) {
          if (isset($vats[$lvat]) && $vats[$lvat] != 0) {
            $this->Cell($width, 5, __("BTW") . " " . $lvat . "%", '', 0, 'R');
            $this->Cell($w[7], 5, '€ ' . getLocalePrice($vats[$lvat], ',', '.'), '', 0, 'R', 0);
            $this->Ln();
          }
        }
        if ($defaultvat == 0 || $invoice->type == Invoice::INVOICE_TYPE_BTWVERLEGD) {
          $this->Cell($width, 5, __("BTW verlegd"), '', 0, 'R');
          $this->Cell($w[7], 5, '€ 0.00', '', 0, 'R', 0);
          $this->Ln();
        }

        //totaal
        $this->SetFont($this->fontname, 'B', 9);
        $this->Cell($width, 1, '', '');
        $this->Cell($w[7], 1, '', 'T');
        $this->Ln();
        $this->Cell($width, 5, __("Totaal"), '', 0, 'R');
        if ($invoice->total < 0) {
          $this->SetTextColor(255, 0, 0);
        }
        $this->Cell($w[7], 5, '€ ' . getLocalePrice($invoice->total, ',', '.'), '', 0, 'R', 0);

        $this->Ln();
        $this->Ln();
        $this->SetTextColor(0);
        $this->SetFont($this->fontname, 'B');

        $value = "";
        $this->SetTextColor(255, 0, 0);


        $width = $w[0] + $w[1] + $w[2] + $w[3] + $w[6] + $w[7];
        if ($hasdiscount) {
          $width += $w[4] + $w[5];
        }
        $this->MultiCell($width, 5, $value, 0, 'L');
        $this->SetTextColor(0, 0, 0);
        $this->SetFont($this->fontname, '');

        $this->setPagenumber($pagecount);

        $value = "Let op: dit is geen factuur.";
        $this->MultiCell($width, 5, __($value), 0, 'L');

        Trans::clearTrans();
        Trans::loadMainLanguagefile();


      }

      // multiple invoices in a single PDF have a batch name
      if (count($invoices) > 1) {
        $this->filename = 'batch_' . date('YmdHis') . '.pdf';
      }

      $this->SetDisplayMode('real');

//      $this->Output();exit;
      $this->Output("F", DIR_TEMP . $this->filename);
      if ($this->redirect) {
        ResponseHelper::redirect(URL_TEMP . $this->filename . '?time=' . time());
      }

    }

  }
