<?php

  use gsdfw\domain\mail\service\WrapperService;
  use gsdfw\domain\mail\service\WrapperServiceCT;
  use PhpOffice\PhpSpreadsheet\Cell\DataType;
  use PhpOffice\PhpSpreadsheet\IOFactory;
  use PhpOffice\PhpSpreadsheet\Reader\Xls;
  use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
  use PhpOffice\PhpSpreadsheet\Spreadsheet;
  use PhpOffice\PhpSpreadsheet\Style\Alignment;
  use PhpOffice\PhpSpreadsheet\Style\Border;
  use PhpOffice\PhpSpreadsheet\Style\Fill;

  AppModel::loadBaseClass('BaseUser');

  class UserModel extends BaseUser {

    const USERGROUP_SUPERADMIN = 'SUPERADMIN';
    const USERGROUP_ADMIN = 'ADMIN';
    const USERGROUP_MAGAZIJN = 'MAGAZIJN';
    const USERGROUP_DISTRIBUTEUR = 'DISTRIBUTEUR';
    const USERGROUP_AGENT = 'AGENT';
    const USERGROUP_KAPSALON = 'KAPSALON';
    const USERGROUP_PARTICULIER = 'PARTICULIER';
    const USERGROUP_BEDRIJF = 'BEDRIJF';
    const USERGROUP_MESSAGES = 'MESSAGES'; //@deprecated
    const USERGROUP_VIEWER = 'VIEWER';
    const USERGROUP_KASSIERE = 'KASSIERE';
    const USERGROUP_BOEKHOUDER = 'BOEKHOUDER';
    const USERGROUP_HOVENIER = 'HOVENIER';
    const USERGROUP_ACCOUNTMANAGER = 'ACCOUNTMANAGER';
    const USERGROUP_SALESMAN = 'SALESMAN';
    const USERGROUP_SHOP = 'SHOP';
    const USERGROUP_WERKNEMER = 'WERKNEMER';
    const USERGROUP_WEBSITE_MANAGER = 'WEBSITE_MANAGER';
    const USERGROUP_DEALER = 'DEALER';
    const USERGROUP_SUBDEALER = 'SUBDEALER';
    const USERGROUP_LEVERANCIER = 'LEVERANCIER'; //let op: deze gebruikersgroep heeft eigen pagina's
    const USERGROUP_ONDERAANNEMER = 'ONDERAANNEMER';
    const USERGROUP_FABRIEKSPLANNER = 'FABRIEKSPLANNER'; //farmfrites planner
    const USERGROUP_PLANNER = 'PLANNER';
    const USERGROUP_CLIENT = 'CLIENT';
    const USERGROUP_TEACHER = 'TEACHER';
    const USERGROUP_ADMINISTRATIE = 'ADMINISTRATIE'; //administratie
    const USERGROUP_BEHEERDER = 'BEHEERDER'; //beheerder

    //JAM
    const USERGROUP_FRANCHISEE = 'FRANCHISEE';
    const USERGROUP_RECRUITER = 'RECRUITER';
    const USERGROUP_JONGERE = 'JONGERE';
    const USERGROUP_STARTER = 'STARTER';

    //marital-status
    const MS_TYPE_UNWEDDED = 'unwedded';
    const MS_TYPE_MARRIED = 'married';
    const MS_TYPE_DEVORCED = 'devorced';
    const MS_TYPE_COHABITATION = 'cohabited';
    const MS_TYPE_PARTNERSHIP = 'partnership';

    const MARITAL_STATI = [
      self::MS_TYPE_UNWEDDED     => "Ongehuwd",
      self::MS_TYPE_MARRIED      => "Gehuwd",
      self::MS_TYPE_DEVORCED     => "Duurzaam gescheiden",
      self::MS_TYPE_COHABITATION => "Samenlevingscontract",
      self::MS_TYPE_PARTNERSHIP  => "Geregistreerd partnerschap",
    ];

    const SUPERADMIN_EMAILS = [
      "<EMAIL>"  => "<EMAIL>",
      "<EMAIL>" => "<EMAIL>",
      "<EMAIL>" => "<EMAIL>",
      "<EMAIL>" => "<EMAIL>",
      "<EMAIL>" => "<EMAIL>",
      "<EMAIL>" => "<EMAIL>", //vrij
      "<EMAIL>" => "<EMAIL>", //vrij
      "<EMAIL>" => "<EMAIL>",
      "<EMAIL>" => "<EMAIL>",
    ];

    public ?Organisation $organisation = null;

    /**
     * Get all defined usergroups
     * @return array
     */
    public static function getAllUsergroups(): array {
      $oClass = new ReflectionClass(get_called_class()); //niet __CLASS__ gebruiken, in User.php kunnen ook usergroups gedefinieerd zijn.
      $usergroups = [];
      foreach ($oClass->getConstants() as $k => $const) {
        if (is_string($k) && substr($k, 0, 10) == "USERGROUP_") {
          $usergroups[$k] = $const;
        }
      }
      return $usergroups;
    }

    /**
     * @return mixed
     */
    public function getId() {
      return $this->id;
    }

    /**
     * @return mixed
     */
    public function getLat() {
      return $this->lat;
    }

    /**
     * @return mixed
     */
    public function getLng() {
      return $this->lng;
    }

    /**
     * Get user's e-mailaddress
     * @return string $email
     */
    public function getEmail() {
      return $this->email;
    }

    /**
     * @return mixed
     */
    public function getPhone() {
      return $this->phone;
    }

    /**
     * @return mixed
     */
    public function getCellphone() {
      return $this->cellphone;
    }

    public function getUserDetails($nlbr = '<br/>'): string {
      $string = "";
      if ($nlbr = '<br/>') {
        $string .= "<b>" . $this->getNaam() . "</b>" . $nlbr;
      }
      else {
        $string .= $this->getNaam() . $nlbr;
      }
      if (!empty($this->phone) && $this->phone != ".") $string .= 'Tel: ' . $this->phone . $nlbr;
      if (!empty($this->cellphone) && $this->cellphone != ".") $string .= 'Mob: ' . $this->cellphone . $nlbr;
      if (!empty($this->email)) $string .= 'Email: ' . $this->email . $nlbr;
      if (!empty($this->birthdate) && Config::isTrue("USER_BIRTHDATE_SHOW")) $string .= 'Leeftijd: ' . $this->getAge() . $nlbr;
      return $string;
    }

    /**
     * @param bool $includeinsertion
     * @return string|null
     */
    public function getLastname(bool $includeinsertion = false): ?string {
      if ($includeinsertion && $this->insertion != "") {
        return $this->insertion . " " . $this->lastname;
      }
      return $this->lastname;
    }

    /**
     * Datum gebruiker voor het laatst ingelogd
     * @param string $format
     * @return false|string
     */
    public function getLastLogin(string $format = "d-m-Y H:i:s"): string|false {
      if ($this->lastlogin != "") {
        return date($format, strtotime($this->lastlogin));
      }
      return '';
    }

    /**
     * Get user name and organisation name. example: Foo [Bar]. Where Foo = user and Bar = organisation
     * @param Organisation $organ
     * @param bool $lastnamefirst
     * @return string
     */
    public function getNaamEnBedrijfsnaam($organ = null, bool $lastnamefirst = false): string {
      $string = $this->getNaam($lastnamefirst);
      if ($organ) {
        if ($organ->name != "") $string .= " [" . $organ->name . ']';
      }
      elseif (isset($this->organisation)) {
        if ($this->organisation->name != "") $string .= " [" . $this->organisation->name . "]";
      }
      elseif (isset($this->organisation_id) && $this->organisation_id != null) {
        $name = Organisation::getBedrijfsnaamById($this->organisation_id);
        if (!empty($name)) $string .= " [" . $name . "]";
      }
      return $string;
    }

    /**
     * Get companyname of user
     * @param $organ
     * @param bool $blokhaken
     * @return string
     */
    public function getBedrijfsnaam($organ, bool $blokhaken = true): string {
      $string = "";
      if ($blokhaken) $string .= " [";
      if (in_array($organ->type, [User::USERGROUP_PARTICULIER, User::USERGROUP_JONGERE, User::USERGROUP_STARTER, User::USERGROUP_MESSAGES]) && $organ->name == "") {
        $string .= "-";
      }
      else {
        $string .= $organ->name;
      }

      if ($blokhaken) $string .= "]";
      return $string;
    }

    /**
     * Get full name of user
     * @param bool $lastnamefirst
     * @return string
     */
    public function getNaam(bool $lastnamefirst = false): string {
      $string = "";
      if ($lastnamefirst) {
        if ($this->lastname != "") $string .= $this->lastname . ", ";
        if ($this->firstname != "") $string .= $this->firstname . " ";
        if ($this->insertion != "") $string .= $this->insertion;
      }
      else {
        if ($this->firstname != "") $string .= $this->firstname . " ";
        if ($this->insertion != "") $string .= $this->insertion . " ";
        if ($this->lastname != "") $string .= $this->lastname;
      }
      return $string;
    }

    /**
     * Get short name aka RG
     * @return string
     */
    public function getNameSuperShort(): string {
      return strtoupper(substr($this->firstname, 0, 1) . substr($this->lastname, 0, 1));
    }

    /**
     * Set usergroup: use this function for setting the usergroup.
     * Dirty flag is used for reseting privileges if needed
     * @param string $usergroup
     * @return void
     */
    public function setUsergroup(string $usergroup): void {
      if ($this->usergroup != $usergroup) {
        $this->setDirty("usergroup", $this->usergroup);
        $this->usergroup = $usergroup;
      }
    }

    /**
     * Gets external id
     * @return string
     */
    public function getExternalId() {
      return $this->external_id;
    }

    /**
     * Registreer de laatste login van deze user.
     * @return void
     * @throws GsdDbException
     * @throws GsdException
     */
    public function registerLastlogin(): void {

      $this->mailOwnerFirstLogin();

      $this->lastlogin = date('Y-m-d H:i:s');
      $this->save();

      UserLogin::register($this->id);

    }


    /**
     * Mail de owner van deze user wanneer deze persoon voor de 1ste keer inlogd.
     * Mail template MAIL_LOGIN_FIRSTIME moet aangemaakt zijn.
     * @return void
     * @throws GsdException
     */
    protected function mailOwnerFirstLogin(): void {
      if (!Config::isdefined("LOGIN_MAIL_OWNER_FIRST_LOGIN") || Config::isFalse("LOGIN_MAIL_OWNER_FIRST_LOGIN")) return;
      if ($this->lastlogin != "") return;
      if ($this->usergroup == User::USERGROUP_SUPERADMIN) return; //geen mail over superadmin

      $owner = User::getUserWithOrganById($this->organisation->owner_user_id);
      if ($owner->usergroup == User::USERGROUP_SUPERADMIN) return; //geen mail naar superadmin

      $comm_template = CommunicationTemplate::getByCodeAndLanguage("MAIL_LOGIN_FIRSTIME");

      $subject = $comm_template->getContent()->subject;

      $ws = new WrapperServiceCT($comm_template->getContent());
      $message = $ws->wrap();

      if (str_contains($message, "{email}")) {
        throw new GsdException("Er worden mogen geen wachtwoorden verzonden worden per e-mail. Corrigeer mailtemplate MAIL_LOGIN.");
      }
      $message = str_replace("{user.fullname}", $this->getNaam(), $message);
      $message = str_replace("{company.name}", $this->organisation->getBedrijfsnaam(), $message);

      GsdMailer::build($owner->email, $subject, $message)->send();

    }


    public function save(&$errors = []) {
      if ($this->from_db == false || $this->insertTS == "0000-00-00 00:00:00") {
        $this->insertTS = date('Y-m-d H:i:s');
        if (isset($_SESSION['userId'])) {
          $this->insertUser = $_SESSION['userId'];
        }
      }
      $this->updateTS = date('Y-m-d H:i:s');
      if (isset($_SESSION['userId'])) {
        $this->updateUser = $_SESSION['userId'];
      }

      $reset_privileges = $this->id == "" || $this->hasDirty("usergroup"); //when is new or usergroup has changed

      $result = parent::save($errors);

      if ($reset_privileges) {
        PrivilegeUser::resetUser($this);
      }

      return $result;
    }

    /**
     * @param $secret
     * @return User|false
     */
    public function getUserBySecret($secret): User|false {
      return User::getUserWithOrgan(['secret' => $secret, 'maylogin' => 1]);
    }

    /**
     * Get users by usergroup
     * @param string|array $usg: string or array with usergroups
     * @return User[]
     */
    public static function getUsersByUsergroup(string|array $usg): array {
      return AppModel::mapObjectIds(User::find_all_by(['usergroup' => $usg, "void" => 0], "ORDER BY lastname"));
    }

    /**
     * @param string|null $email
     * @param string $usergroup
     * @param $notcheckthisuserid
     * @return bool
     */
    public static function isEmailUnique(?string $email, string $usergroup = '', $notcheckthisuserid = ''): bool {
      $props = ['email' => $email];
      if ($usergroup != '') {
        $props['usergroup'] = $usergroup;
      }
      if (Config::isTrue("USER_VOID_ON_DESTROY")) {
        $props['void'] = "0";
      }
      $filt = "";
      if ($notcheckthisuserid != '') {
        $filt = " AND id !='" . escapeForDB($notcheckthisuserid) . "'";
      }

      $result = User::find_all_by($props, $filt);
      if (count($result) == 0) {
        return true;
      }
      return false;
    }

    /**
     * Controleer of emailadres en wachtwoord combinatie uniek is.
     * @param string|null $email
     * @param string|null $password
     * @param string $usergroup
     * @param $notcheckthisuserid
     * @return bool
     */
    public static function isEmailPasswordUnique(?string $email, ?string $password, string $usergroup = '', $notcheckthisuserid = ''): bool {
      $props = ['email' => $email, 'password' => $password];
      if ($usergroup != '') {
        $props['usergroup'] = $usergroup;
      }
      if (Config::isTrue("USER_VOID_ON_DESTROY")) {
        $props['void'] = "0";
      }
      $result = User::find_all_by($props, " AND id !='" . $notcheckthisuserid . "'");
      if (count($result) == 0) {
        return true;
      }
      return false;
    }

    /**
     * @param bool $persoonlijk
     * @return string
     */
    public function getAanhef(bool $persoonlijk = false): string {
      if ($persoonlijk) {
        return $this->firstname;
      }
      $string = "";
      if (Gender::MALE == $this->sex) $string .= __('heer');
      if (Gender::FEMALE == $this->sex) $string .= __('mevrouw');
      $string .= " ";
      if ($this->insertion != "") $string .= $this->insertion . " ";
      if ($this->lastname != "") $string .= $this->lastname;

      return $string;
    }

    /**
     * @return string
     */
    public function getSexDescription(): string {
      if (Gender::MALE == $this->sex) return __('Man');
      if (Gender::FEMALE == $this->sex) return __('Vrouw');
      return '';
    }

    /**
     * @param $addinitials
     * @param $shortpref
     * @return string
     */
    public function getNaamEenv($addinitials = false, $shortpref = false): string {
      $string = "";
      if ($shortpref) {
        if (Gender::MALE == $this->sex) $string .= __('Dhr.');
        if (Gender::FEMALE == $this->sex) $string .= __('Mw.');
      }
      else {
        if (Gender::MALE == $this->sex) $string .= __('De heer');
        if (Gender::FEMALE == $this->sex) $string .= __('Mevrouw');
      }
      if ($addinitials && $this->initials != "") {
        $string .= " " . $this->initials;
      }
      if ($this->insertion != "") $string .= " " . $this->insertion;
      if ($this->lastname != "") $string .= " " . $this->lastname;

      return $string;
    }

    /**
     * Get bedrijfs details of this user
     * @param $nl
     * @return string
     */
    public function getNaamBedrijfsDetails($nl = "\n"): string {
      $string = $this->getNaam();
      if ($this->email != "") $string .= $nl . 'Email: ' . $this->email;
      if ($this->phone != "" && $this->phone != ".") $string .= $nl . 'Tel: ' . $this->phone;
      if ($this->cellphone != "" && $this->cellphone != ".") $string .= $nl . 'Mob: ' . $this->cellphone;

      if ($this->organisation_id) {
        $organ = null;
        if (isset($this->organisation)) {
          $organ = $this->organisation;
        }
        else {
          $organ = Organisation::find_by_id($this->organisation_id);
        }
        if ($organ) {
          $string .= $nl . $nl . $organ->getDetails($nl, true);
        }
      }
      return $string;
    }

    public function getEmailFooter(string $nl = "\n"): string {
      $organ = null;
      if (isset($this->organisation)) {
        $organ = $this->organisation;
      }
      else {
        $organ = Organisation::find_by_id($this->organisation_id);
      }
      $string = $this->getNaam() . $nl;
      $string .= $organ->name . $nl;
      $string .= $nl;
      if (!empty($organ->website)) $string .= 'Website: <a href="' . $organ->website . '">' . StringHelper::getDomainOfUrl($organ->website) . "</a>" . $nl;
      if (!empty($this->email)) $string .= 'E-mail: <a href="mailto:' . $this->email . '">' . $this->email . "</a>" . $nl;
      if (!empty($this->phone)) $string .= 'Tel:  <a href="tel:' . $this->phone . '">' . $this->phone . "</a>" . $nl;
      if (!empty($this->cellphone)) $string .= 'Tel:  <a href="tel:' . $this->cellphone . '">' . $this->cellphone . "</a>" . $nl;
      if ($organ->coc_number != "" && $organ->coc_number != ".") $string .= 'KvK: ' . $organ->coc_number . $nl;

      return $string;
    }

    /**
     * Get the usergroups this usergroup may view / edit sorted by name
     * @param string|null $usergroup
     * @return string[]
     * @throws GsdException
     */
    public static function getInternalUsergroupSorted(?string $usergroup = ''): array {
      $usgs = [];
      foreach (self::getInternalUsergroup($usergroup) as $usg) {
        $usgs[$usg] = User::getInternalUsergroupDesc($usg);
      }
      asort($usgs);
      return $usgs;
    }

    /**
     * Get the usergroups this usergroup may view / edit
     * @param string|null $userusergroup
     * @return string[]
     * @throws GsdException
     */
    public static function getInternalUsergroup(?string $userusergroup = ''): array {

      $usgs = Config::get('usergroups_for_usg');

      if ($userusergroup == User::USERGROUP_SUPERADMIN) {
        //alle gebruikte usergroups teruggeven van dit project
        if (isset($usgs[User::USERGROUP_ADMIN])) {
          return array_merge([User::USERGROUP_SUPERADMIN, User::USERGROUP_ADMIN], $usgs[User::USERGROUP_ADMIN]);
        }
        if (isset($usgs[User::USERGROUP_SUPERADMIN])) {
          return array_merge([User::USERGROUP_SUPERADMIN], $usgs[User::USERGROUP_SUPERADMIN]);
        }
        throw new GsdException("Er is geen gebruikersgroep ADMIN of SUPERADMIN ingericht in Config usergroups_for_usg. Voeg SUPERADMIN toe aan Config usergroups_for_usg in configure_root.");
      }

      if (isset($usgs[$userusergroup])) {
        return $usgs[$userusergroup];
      }
      elseif (isset($usgs['__DEFAULT__'])) {
        return $usgs['__DEFAULT__'];
      }

      return [];

    }

    /**
     * Welke usergroepen zijn mogelijk bij welk organisatie type.
     * @param string|null $organtype
     * @return string[]
     * @throws Exception
     */
    public static function getInternalUsergroupFromOrgantype(?string $organtype = '') {
      if ($_SESSION['userObject']->usergroup == User::USERGROUP_SUPERADMIN) {
        $usgs = Config::get('usergroups_for_organtype', true);
        if ($usgs && isset($usgs[$organtype])) {
          return $usgs[$organtype];
        }

        return [
          User::USERGROUP_ADMIN,
          User::USERGROUP_MAGAZIJN,
          User::USERGROUP_DISTRIBUTEUR,
          User::USERGROUP_AGENT,
          User::USERGROUP_KAPSALON,
          User::USERGROUP_PARTICULIER,
          User::USERGROUP_BEDRIJF,
          User::USERGROUP_MESSAGES,
          User::USERGROUP_VIEWER,
          User::USERGROUP_BOEKHOUDER,
          User::USERGROUP_HOVENIER,
          User::USERGROUP_SALESMAN,
          User::USERGROUP_ACCOUNTMANAGER,
          User::USERGROUP_SHOP,
          User::USERGROUP_WERKNEMER,
          User::USERGROUP_WEBSITE_MANAGER,
          User::USERGROUP_DEALER,
          User::USERGROUP_SUBDEALER,
          User::USERGROUP_LEVERANCIER,
          User::USERGROUP_ONDERAANNEMER,
          User::USERGROUP_FABRIEKSPLANNER,
          User::USERGROUP_PLANNER,
          User::USERGROUP_ADMINISTRATIE,
          User::USERGROUP_CLIENT,
          User::USERGROUP_TEACHER,
          User::USERGROUP_BEHEERDER,
        ];
      }

      if ($organtype == '') {
        return [];
      }

      $usgs = Config::get('usergroups_for_organtype', true);
      if ($usgs && isset($usgs[$organtype])) {
        return $usgs[$organtype];
      }

      if ($organtype == Organisation::TYPE_OTHER) {
        return [
          User::USERGROUP_SALESMAN,
          User::USERGROUP_ACCOUNTMANAGER,
          User::USERGROUP_WERKNEMER,
          User::USERGROUP_WEBSITE_MANAGER,
          User::USERGROUP_CLIENT,
          User::USERGROUP_ADMINISTRATIE,
          User::USERGROUP_BEHEERDER,
          User::USERGROUP_PLANNER,
        ];
      }
      elseif ($organtype == Organisation::TYPE_DISTRIBUTEUR) {
        return [
          User::USERGROUP_DISTRIBUTEUR,
        ];
      }
      elseif ($organtype == Organisation::TYPE_AGENT) {
        return [
          User::USERGROUP_AGENT,
        ];
      }
      elseif ($organtype == Organisation::TYPE_KAPSALON) {
        return [
          User::USERGROUP_KAPSALON,
        ];
      }
      elseif ($organtype == Organisation::TYPE_PARTICULIER) {
        return [
          User::USERGROUP_PARTICULIER,
        ];
      }
      elseif ($organtype == Organisation::TYPE_HOVENIER) {
        return [
          User::USERGROUP_HOVENIER,
        ];
      }
      elseif ($organtype == Organisation::TYPE_BEDRIJF) {
        return [
          User::USERGROUP_BEDRIJF,
        ];
      }
      elseif ($organtype == Organisation::TYPE_SHOP) {
        return [
          User::USERGROUP_SHOP,
        ];
      }
      elseif ($organtype == Organisation::TYPE_DEALER) {
        return [
          User::USERGROUP_DEALER,
        ];
      }
      elseif ($organtype == Organisation::TYPE_SUBDEALER) {
        return [
          User::USERGROUP_SUBDEALER,
        ];
      }
      elseif ($organtype == Organisation::TYPE_LEVERANCIER) {
        return [
          User::USERGROUP_LEVERANCIER,
        ];
      }
      elseif ($organtype == Organisation::TYPE_ONDERAANNEMER) {
        return [
          User::USERGROUP_ONDERAANNEMER,
        ];
      }
      elseif ($organtype == Organisation::TYPE_FABRIEKSPLANNER) {
        return [
          User::USERGROUP_FABRIEKSPLANNER,
        ];
      }
      elseif ($organtype == Organisation::TYPE_FRANCHISEE) {
        return [
          User::USERGROUP_FRANCHISEE,
        ];
      }
      elseif ($organtype == Organisation::TYPE_JONGERE) {
        return [
          User::USERGROUP_JONGERE,
        ];
      }
      elseif ($organtype == Organisation::TYPE_SCHOOL) {
        return [
          User::USERGROUP_TEACHER,
        ];
      }
      return [];
    }

    /**
     * Check if user may login. Is used for backend and frontend.
     *
     * @param string $email (required)
     * @param string $password (required)
     * @param bool $ignorepasword (optional) default false
     * @param null $usergroups (optional) default null
     * @param null $userid (optional) default null
     * @param bool $ignoremaylogin
     * @return null|false|User
     * @throws GsdDbException
     * @throws GsdException
     */
    public static function login($email, $password, $ignorepasword = false, $usergroups = null, $userid = null, $ignoremaylogin = false) {

      $user = false;
      $props = [];
      if (!$ignoremaylogin) {
        $props['maylogin'] = 1;
      }

      if ($usergroups != null && $usergroups !== false) {
        $props['usergroup'] = $usergroups;
      }
      if ($userid != null) {
        $props['id'] = $userid;
      }
      if (Config::isTrue("USER_VOID_ON_DESTROY")) {
        $props['void'] = "0";
      }

      if (!$ignorepasword && Config::isTrue("USER_EMAIL_NOTUNIQUE_PW")) {
        $props['password'] = $password;
        if (Config::isTrue("USER_PASSWORD_ENCRYPT")) {
          $props['password'] = User::encrypt($password);
        }
      }

      if (Config::isTrue("USER_USERNAME_ENABLED")) {

        //login using username
        $props['username'] = $email;
        //check if usergroup is in allowed array of users who may login using theire username
        if (Config::isdefined('USER_USERNAME_USERGROUPS_LOGIN')) {
          $props['usergroup'] = Config::get('USER_USERNAME_USERGROUPS_LOGIN');
        }
        $user = User::getUserWithOrgan($props);
      }

      if (!$user) {

        //login using email
        if (isset($props['username'])) {
          unset($props['username']);
          unset($props['usergroup']);
        }
        $props['email'] = $email;
        $raw_sql = '';
        if (Config::isdefined('USER_USERNAME_USERGROUPS_LOGIN')) {
          $raw_sql = " AND usergroup NOT IN ('" . implode("','", Config::get('USER_USERNAME_USERGROUPS_LOGIN')) . "') ";
        }
        $user = User::getUserWithOrgan($props, $raw_sql);
      }

      //check if user's password is correct
      if ($user && !$ignorepasword) {

        // De user is gevonden, echter het wachtwoord is leeg in de database. Verzoek om wachtwoord vergeten functionaliteit te gebruiken voor een wachwoord reset.
        if (empty($user->password) || (Config::isTrue("PASSWORD_EXPIRATION") && $user->isPasswordExpired())) {
          $mes = __("Uw account is gevonden, echter uw wachtwoord is verlopen. U kunt een nieuwe wachtwoord aanvragen via de 'Wachtwoord vergeten' functionaliteit op de inloggen pagina.");
          if(!MessageFlashCoordinator::hasMessage($mes)) { //het kan zijn dat meerdere keren deze functie doorloopt. Eigenlijk moet je dit oplossen met een exception
            MessageFlashCoordinator::addMessageAlert($mes);
          }
          return null;
        }

        if (
          (Config::isTrue("USER_PASSWORD_ENCRYPT") && User::encrypt($password) === $user->password) || //encryption
          (preg_match('/^[a-f0-9]{32}$/', $user->password) && md5($password) === $user->password) || //md5 hashed
          ($user->password === $password) //plaintext
        ) {
          //password is correct, but still in high security risk, convert to php hash password
          if (Config::isTrue("USER_PASSWORD_ENCRYPT")) {
            $user->password = User::encrypt($password);
            $user->save();
          }
        }
        else { //incorrect password
          sleep(1);
          $user = null;
        }

      }

      return $user;
    }


    /**
     * Login with a login token, which is set in a cookie
     *
     * @param string $email E-mail or username
     * @param string $login_token login token (decrypted)
     * @param null $usergroups
     * @return User|false
     * @throws Exception
     */
    public static function loginByToken($email, $login_token, $usergroups = null): User|false {
      $user = null;
      $props = [];

      $props['maylogin'] = 1;

      if ($usergroups != null && $usergroups !== false) {
        $props['usergroup'] = $usergroups;
      }
      if (Config::isTrue("USER_VOID_ON_DESTROY")) {
        $props['void'] = "0";
      }

      $props['login_token'] = $login_token;

      if (Config::isTrue("USER_USERNAME_ENABLED")) {
        //login using username
        $props['username'] = $email;
        //check if usergroup is in allowed array of users who may login using theire username
        if (Config::isdefined('USER_USERNAME_USERGROUPS_LOGIN')) {
          $props['usergroup'] = Config::get('USER_USERNAME_USERGROUPS_LOGIN');
        }
        $user = User::getUserWithOrgan($props);
      }

      if (!$user) {
        //login using email
        if (isset($props['username'])) {
          unset($props['username']);
          unset($props['usergroup']);
        }
        $props['email'] = $email;
        $raw_sql = '';
        if (Config::isdefined('USER_USERNAME_USERGROUPS_LOGIN')) {
          $raw_sql = " AND usergroup NOT IN ('" . implode("','", Config::get('USER_USERNAME_USERGROUPS_LOGIN')) . "') ";
        }
        $user = User::getUserWithOrgan($props, $raw_sql);
      }

      return $user;
    }

    /**
     * Get password decrypted
     * @string decrypted password
     */
    public function getPasswordDecrypted() {
      return User::decrypt($this->password);
    }

    /**
     * Determine if password is valid.
     * Conditions:
     * length > 5 characters, min hoofdletter, kleine letter en cijfer
     * allowed characters:  [A-Z a-z 0-9 - _ ]
     * @return bool isvalid
     */
    public function validPassword() {
      $pwd = $this->getPasswordDecrypted();
      return (
        strlen($pwd) >= 5
        && !preg_match('/[^A-Za-z0-9.#\\-_$]/', $pwd)
        && preg_match("#[a-z]+#", $pwd)
        && preg_match("#[A-Z]+#", $pwd)
        && preg_match("#[0-9]+#", $pwd)
      );
    }

    /**
     * Encrypts password
     * @param string $string plaintext password
     * @return string encrypted password
     */
    public static function encrypt($string) {
      return EncryptionHelper::encrypt($string);
    }

    /**
     * Decrypts password
     * @param string $string
     * @return string decrypted password
     */
    public static function decrypt($string) {
      return EncryptionHelper::decrypt($string);
    }

    /**
     * Get description of usergroup
     * @param string $type
     * @return string
     */
    public static function getInternalUsergroupDesc($type): string {
      if ($type == null || $type == '') return "";
      if (Trans::hasValue("USERGROUP_" . $type)) {
        return __("USERGROUP_" . $type);
      }
      return ucfirst(strtolower($type));
    }

    /**
     * @param array|null $conditions (required)
     * @param string $raw_sql (optional)
     * @return User|false
     */
    public static function getUserWithOrgan(?array $conditions, string $raw_sql = ''): User|false {
      $user = self::find_by($conditions, $raw_sql);
      if ($user) {
        $user->organisation = Organisation::find_by_id($user->organisation_id);
      }
      return $user;
    }

    /**
     * Gets user and organisation by user.id
     * @param $id : userId
     * @return User|false
     */
    public static function getUserWithOrganById($id): User|false {
      $user = self::find_by_id($id);
      if ($user) {
        $user->organisation = Organisation::find_by_id($user->organisation_id);
      }
      return $user;
    }

    /**
     * Get users by usergroups array
     * @param string[] $usergroups
     * @param bool|null $void : null=ignore void
     * @param string $orderby
     * @return User[]
     * @throws GsdException
     */
    public static function getUsersWithOrganByUsergroups(array $usergroups, ?bool $void = false, string $orderby = "ORDER BY lastname"): array {
      if (count($usergroups) == 0) return [];
      $filt = '';
      if ($void !== null) {
        $filt .= "AND user.void=" . ($void ? 1 : 0) . " AND organisation.void=" . ($void ? 1 : 0) . " ";
      }
      $filt .= "AND usergroup IN ('" . implode("','", $usergroups) . "') ";
      $filt .= $orderby;
      return User::getUsersWithOrgan($filt);
    }

    /**
     * Get users by filter
     * @param string $filter
     * @return User[]
     * @throws GsdException
     */
    public static function getUsersWithOrgan(string $filter = ""): array {
      $query = "SELECT * FROM organisation ";
      $query .= "JOIN user ON organisation.id=user.organisation_id ";
      $query .= "WHERE 1 ";
      $query .= $filter;
      //echo $query;
      $result = DBConn::db_link()->query($query);
      $users = [];
      while ($row = $result->fetch_row()) {
        $column_counter = 0;
        $organisation = new Organisation()->hydrateNext($row, $column_counter);
        $user = new User()->hydrateNext($row, $column_counter);
        $user->organisation = $organisation;
        $users[$user->id] = $user;
      }
      return $users;
    }

    /**
     * @param bool $show_inactive (optional) default false
     * @return User[]
     */
    public static function getEmployees(bool $show_inactive = false): array {
      $filt_arr = [
        'organisation_id' => ADMIN_DEFAULT_ID,
        'usergroup'       => User::USERGROUP_WERKNEMER,
      ];
      if (!$show_inactive) {
        $filt_arr['active'] = true;
      }

      return AppModel::mapObjectIds(User::find_all_by($filt_arr, 'ORDER BY lastname'));
    }

    /**
     * Remove inactive users from user array
     * @param User[] $employees
     * @return User[]
     */
    public static function filterEmpoyeesActive(array $employees): array {
      foreach ($employees as $k => $emp) {
        if ($emp->active == 0) {
          unset($employees[$k]);
        }
      }
      return $employees;
    }

    /**
     * @param string $format
     * @return string|false
     */
    public function getBirthdate($format = "d-m-Y"): string|false {
      if (!$this->birthdate || $this->birthdate == '0000-00-00') {
        return '';
      }
      return date($format, strtotime($this->birthdate));
    }

    /**
     * Get's age of user
     * @param bool|string $ondate determine date on 2017-05-12
     * @return bool|int
     */
    public function getAge($ondate = false): int|false {

      if (empty($this->birthdate)) return false;
      if (!$ondate) {
        $ondate = date('Y-m-d');
      }
      $ondate_time = strtotime($ondate);
      $birthdate_ts = strtotime($this->birthdate);

      $age = date('Y', $ondate_time) - date('Y', $birthdate_ts);
      if (date('md', $birthdate_ts) > date('md', $ondate_time)) {
        $age--;
      }
      return $age;
    }

    /**
     * Get keys for mail templates
     * @param User $user
     * @param Invoice|null $invoice
     * @param array $keys
     * @return array
     */
    public static function getKeys(?User $user, ?Invoice $invoice = null): array {
      if ($user == null) return [];
      $keys = [];

      $gender = '';
      if (Gender::MALE == $user->sex) $gender .= __('De heer');
      if (Gender::FEMALE == $user->sex) $gender .= __('Mevrouw');
      $keys['{name}'] = trim($gender . ' ' . ($user->initials != "" ? $user->initials : ucfirst($user->firstname)) . ' ' . $user->insertion . ' ' . ucfirst($user->lastname));

      $organ = Organisation::find_by_id($user->organisation_id);

      if (isset($organ->address)) {
        $address = "";
        if ($organ->address . $organ->number != "") $address .= $organ->address . " " . $organ->number;
        if ($organ->zip . $organ->city != "") $address .= "\n" . $organ->zip . " " . $organ->city;
        $keys['{address}'] = trim($address);
      }
      $keys['{today}'] = intval(date('d')) . ' ' . DateTimeHelper::strftime("%B %Y");

      $gender = '';
      if (Gender::MALE == $user->sex) $gender .= __('heer');
      if (Gender::FEMALE == $user->sex) $gender .= __('mevrouw');
      $aanhef = $gender;
      if (trim($user->insertion) != "") $aanhef .= ' ' . ucfirst(trim($user->insertion));
      if (trim($user->lastname) != "") $aanhef .= ' ' . ucfirst(trim($user->lastname));
      $keys['{beginning}'] = $aanhef;

      if ($invoice != null) {
        $keys['{invoicenumber}'] = $invoice->invoice_nr;
        //$keys['{quotenumber}'] = $invoice->getOrderNr($invoice->order_id);
      }

      return $keys;
    }

    /**
     * Get invoice email addresses
     * @return array
     * @throws GsdException
     */
    public function getInvoiceTos(): array {
      //is er een factuur emailadres?
      $tos = [];
      if (ValidationHelper::isEmail($this->organisation->email_invoice)) {
        $tos[] = $this->organisation->email_invoice;
      }
      if (!Config::isdefined("INVOICE_SEND_INVOICE_EMAIL_ONLY") || Config::isFalse("INVOICE_SEND_INVOICE_EMAIL_ONLY")) {
        //users met receives_invoice vlag?
        $organusers = User::getUsersWithOrgan('AND organisation_id=' . $this->organisation_id);
        foreach ($organusers as $luser) {
          if ($luser->receives_invoice == 1 && ValidationHelper::isEmail($luser->email)) {
            $tos[] = $luser->email;
          }
        }
        if (count($tos) == 0) { //nog geen email, dan naar 1e user uit organisatie met een emailadres
          foreach ($organusers as $luser) {
            if (ValidationHelper::isEmail($luser->email)) {
              $tos[] = $luser->email;
              break;
            }
          }
        }
      }
      return $tos;
    }

    /**
     * Get mail suffix for this user
     * @param string $nlbr
     * @return string
     */
    public function getMailSuffix(string $nlbr = '<br/>'): string {
      $string = '';
      if ($this->firstname != "") $string .= $this->firstname . " ";
      if ($this->insertion != "") $string .= $this->insertion . " ";
      if ($this->lastname != "") $string .= $this->lastname . $nlbr . $nlbr;
      $organ = Organisation::find_by_id($this->organisation_id);
      if ($organ) {
        if ($organ->name != "") $string .= $organ->name . $nlbr . $nlbr;
        if ($organ->address . $organ->number != "") $string .= $organ->address . " " . $organ->number . $nlbr;
        if ($organ->zip . $organ->city != "") $string .= $organ->zip . " " . $organ->city . $nlbr;
      }
      if ($this->phone != "") $string .= 'Tel: ' . StringHelper::asPhonenumber($this->phone) . $nlbr;
      if ($this->cellphone != "") $string .= 'Mob: ' . StringHelper::asPhonenumber($this->cellphone) . $nlbr;
      if ($this->email != "") $string .= 'Email: ' . $this->email . $nlbr;
      return $string;
    }

    /**
     * Set user to inactive.
     * This means that the user may not login anymore an is inactive, but is not deleted.
     * @return void
     */
    public function setInactive(): void {
      $this->active = 0;
      $this->maylogin = 0;
    }

    /**
     *
     * Check if given user may edit.
     * @param int $to_organ_id (required)
     * @param User|bool $from_user (optional)
     * @return bool
     * @throws GsdException
     */
    public static function mayEdit($to_organ_id, $from_user = false): bool {
      if (isset($_SESSION['userObject']) && $_SESSION['userObject'] != "") {
        $from_user = $_SESSION['userObject'];
      }

      if (!$from_user) return false;

      if (isset($_SESSION['mayEdit'][$from_user->id][$to_organ_id])) { //performance!!
        return $_SESSION['mayEdit'][$from_user->id][$to_organ_id];
      }
      if (isset($_SESSION['mayEdit'][$from_user->id]['*'])) { //alle kinderen gehad. Dus geen rechten.
        return false;
      }
      $to_organ = Organisation::find_by_id($to_organ_id);
      if (!$to_organ) return false;
      $result = false;
      //check if to_user is a child of from_user
      if ($from_user->usergroup == User::USERGROUP_SUPERADMIN) {
        $result = true;
      }
      elseif ($from_user->organisation_id == $to_organ->owner_organisation_id) { //ik ben eigenaar van deze organisatie..editen maar.
        $result = true;
      }
      elseif (($from_user->usergroup == User::USERGROUP_ADMIN || $from_user->usergroup == User::USERGROUP_MAGAZIJN || $from_user->usergroup == User::USERGROUP_KASSIERE || $from_user->usergroup == User::USERGROUP_BEHEERDER) && $to_organ->type != Organisation::TYPE_OTHER) {
        $result = true;
      }
      else {
        if ($from_user->organisation_id == $to_organ->id) {
          //mag zichzelf editen.
          $result = true;
        }
        elseif ($from_user->organisation->owner_organisation_id == $to_organ->id) {
          //ingelogde organisatie mag nooit zijn eigen eigenaar bewerken
          $result = false;
        }
        else {

          if ($from_user->usergroup == User::USERGROUP_FRANCHISEE && $to_organ->type == Organisation::TYPE_FRANCHISEE && Config::isdefined("QUICK_SWITCH")) {
            $switch_arr = Config::get("QUICK_SWITCH");
            if (isset($switch_arr[$from_user->organisation_id]) && in_array($to_organ->id, $switch_arr[$from_user->organisation_id])) {
              $_SESSION['mayEdit'][$from_user->id][$to_organ->id] = true;
              return true;
            }
          }

          //loopen over alle kinderen en bepaal rechten.
          $head1 = self::getChildrenFlat($from_user->organisation_id); //alle kinderen van deze eigenaar=from_user.
          $children = [];
          foreach ($head1 as $key => $par) {
            //alle organisaties waarvan je eigenaar bent mag je editen. oftwel al mijn kinderen
            $_SESSION['mayEdit'][$from_user->id][$par->id] = true;
            $children[$key] = $par;
          }

          if (isset($_SESSION['mayEdit'][$from_user->id][$to_organ_id])) { //ondertussen hebben we de gewenste organisatie gevonden? Terug maar.
            return true;
          }

          foreach ($children as $child) {
            //AGENTS
            $childs = self::getChildrenFlat($child->id);//alle kleinkinderen
            foreach ($childs as $id => $ch) {
              $_SESSION['mayEdit'][$from_user->id][$ch->id] = true;
              if ($ch->type == 'KAPSALON') { //alleen bij kapsalon een niveau lager kijken. (je bent ingelogd als distributeur)
                foreach (self::getChildrenFlat($ch->id) as $id2 => $ch2) {
                  $_SESSION['mayEdit'][$from_user->id][$ch2->id] = true;
                }
              }
            }
          }
          $_SESSION['mayEdit'][$from_user->id]['*'] = false;
          if (isset($_SESSION['mayEdit'][$from_user->id][$to_organ_id])) { //ondertussen hebben we de gewenste organisatie gevonden? Terug maar.
            return true;
          }
          return $_SESSION['mayEdit'][$from_user->id][$to_organ_id];
        }
      }
      $_SESSION['mayEdit'][$from_user->id][$to_organ_id] = $result;
      return $result;
    }

    /**
     * @return array ['success' => bool, 'message' (optional) => string]
     */
    public function mayDelete(): array {
      if (Orders::tableExists() && Orders::find_by(['user_id' => $this->id])) {
        return [
          'success' => false,
          'message' => "Persoon is NIET verwijderd. Deze gebruiker heeft bestellingen/offertes gekoppeld. U kunt de bestellingen/offertes verwijderen en dan nogmaals proberen, of de persoon op inactief zetten.",
        ];
      }
      if (Invoice::tableExists() && Invoice::find_by(['user_id' => $this->id])) {
        return [
          'success' => false,
          'message' => "Persoon is NIET verwijderd. Deze gebruiker heeft facturen gekoppeld. U kunt de facturen verwijderen en dan nogmaals proberen, of de persoon op inactief zetten.",
        ];
      }
      return ['success' => true];
    }

    /**
     * Check if user has rights to switch to another user.
     * @param int $to_user_id
     * @param int|false $from_user_id
     * @return bool
     */
    public static function hasRightsToSwitch($to_user_id, $from_user_id = false): bool {
      $to_user = User::getUserWithOrganById($to_user_id);
      if (!$to_user) return false;

      $from_user = null;
      if ($from_user_id) {
        $from_user = User::getUserWithOrganById($from_user_id);
      }
      elseif (isset($_SESSION['userObject']) && $_SESSION['userObject'] != "") {
        $from_user = $_SESSION['userObject'];
      }
      if (!$from_user) return false;
      if ($from_user->maylogin != 1) return false; //mag niet inloggen, dus ook niet naar switchen.
      return User::mayEdit($to_user->organisation_id, $from_user);
    }

    /**
     * Get all owned organisation by this organisation
     * @param Organisation $parent
     * @return Organisation[]
     */
    public static function getChildren(Organisation $parent): array {
      return Organisation::getOrganisationsByOwnerId($parent);
    }

    /**
     * @param int $owner_organ_id
     * @return array
     * @throws GsdException
     */
    public static function getChildrenFlat($owner_organ_id): array { //dit is voor performance!
      $query = 'SELECT id, owner_organisation_id, type FROM organisation ';
      $query .= 'WHERE organisation.owner_organisation_id = ' . escapeForDB($owner_organ_id) . ' ';
      $query .= 'AND organisation.id != ' . escapeForDB($owner_organ_id);

      $result = DBConn::db_link()->query($query);
      $organs = [];
      while ($row = $result->fetch_object()) {
        $organs[$row->id] = $row;
      }
      return $organs;
    }


    /**
     * Get parents (max is dist)
     * @return User[]
     */
    public function getParents(): array {
      $parents = [];
      $current = User::getUserWithOrganById($this->id);
      while ($current && $current->usergroup != User::USERGROUP_DISTRIBUTEUR && $current->usergroup != User::USERGROUP_ADMIN) {
        $current = User::getUserWithOrganById($current->organisation->owner_user_id);
        $parents[] = $current;
      }
      return $parents;
    }

    /**
     * Get possible owners for this organisation
     * @param Organisation $organ (required)
     * @return User[]
     * @throws GsdDbException
     * @throws GsdException
     */
    public static function getPossibleOwners($organ = false): array {
      $filt = "";
      $order = "";
      if ($organ->type == Organisation::TYPE_OTHER && $_SESSION['userObject']->usergroup == User::USERGROUP_SUPERADMIN) {
        $filt = " AND " . DbHelper::getSqlIn("usergroup", [User::USERGROUP_ADMIN, User::USERGROUP_MAGAZIJN, User::USERGROUP_DISTRIBUTEUR]);
        $order = "ORDER BY lastname ";
      }
      elseif ($organ->type == Organisation::TYPE_AGENT || $organ->type == Organisation::TYPE_FRANCHISEE) {
        $filt = " AND usergroup = '".User::USERGROUP_DISTRIBUTEUR."' ";
        $order = "ORDER BY lastname ";
      }
      elseif ($organ->type == Organisation::TYPE_KAPSALON) {
        $filt = " AND " . DbHelper::getSqlIn("usergroup", [User::USERGROUP_AGENT,User::USERGROUP_DISTRIBUTEUR]);
        $order = "ORDER BY lastname ";
      }
      elseif ($organ->type == Organisation::TYPE_HOVENIER) {
        $filt = " AND " . DbHelper::getSqlIn("usergroup", [User::USERGROUP_ADMIN, User::USERGROUP_SALESMAN]);
      }
      elseif ($organ->type == Organisation::TYPE_PARTICULIER || $organ->type == Organisation::TYPE_BEDRIJF || $organ->type == Organisation::TYPE_JONGERE || $organ->type == Organisation::TYPE_STARTER) {
        if (PROJECT == 'bodel') {
          $filt = " AND usergroup = '".User::USERGROUP_ACCOUNTMANAGER."' ";
          $order = "ORDER BY lastname ";
        }
        elseif (PROJECT == 'jam') {
          if ($_SESSION['userObject']->usergroup == User::USERGROUP_DISTRIBUTEUR) {
            $filt .= "AND organisation.owner_organisation_id = " . escapeForDB($_SESSION['userObject']->organisation_id) . " ";
          }
          elseif ($_SESSION['userObject']->usergroup == User::USERGROUP_FRANCHISEE) {
            $filt .= "AND organisation.owner_organisation_id = " . escapeForDB($_SESSION['userObject']->organisation->owner_organisation_id) . " ";
          }
          $filt .= " AND usergroup IN ('FRANCHISEE') AND organisation.void = 0 ";
          $order = "ORDER BY organisation.name, user.lastname ";
        }
        else {
          $filt = " AND usergroup = '".User::USERGROUP_ADMIN."' ";
          $order = "ORDER BY lastname ";
        }
      }

      if ($filt != "") {
        if (Config::isTrue("USER_VOID_ON_DESTROY")) {
          $filt .= "AND user.void = 0 ";
        }
        $filt .= $order;
        return User::getUsersWithOrgan($filt);
      }
      return [];
    }

    /**
     * @param mixed $options
     */
    public function setOptions($options): void {
      $this->options = serialize($options);
    }

    /**
     * @return mixed
     */
    public function getOptions() {
      return unserialize($this->options ?? '');
    }

    /**
     * @param string|int $option (required)
     * @return mixed
     */
    public function getOption($option) {
      $options = $this->getOptions();
      if (isset($options[$option])) {
        return $options[$option];
      }
      return '';
    }

    /**
     * @param mixed $option
     * @param mixed $value
     * @return bool
     */
    public function hasOption($option, $value): bool {
      $options = $this->getOptions();
      if (isset($options[$option]) && $options[$option] === $value) {
        return true;
      }

      return false;
    }

    public static function sortByLastname($a, $b): int {
      $val = strcasecmp($a->lastname ?? '', $b->lastname ?? '');
      if ($val == 0) {
        $val = strcasecmp($a->firstname ?? '', $b->firstname ?? '');
      }
      return $val;
    }

    /**
     * Check if user may login to backend
     * @return bool
     */
    public function hasBackendLogin(): bool {
      if ($this->maylogin == 1 && $this->usergroup != User::USERGROUP_PARTICULIER) {
        return true;
      }
      return false;
    }

    /**
     * @param User $user (required)
     * @return string
     * @throws Exception
     */
    public static function getDirectLoginLink(User $user): string {
      $props = [];
      $props['email'] = urlencode($user->email ?? '');
      if ($user->usergroup && Config::isdefined("USER_USERNAME_ENABLED") && Config::isdefined("USER_USERNAME_USERGROUPS_LOGIN") && in_array($user->usergroup, Config::get("USER_USERNAME_USERGROUPS_LOGIN"))) {
        unset($props['email']);
        $props['username'] = urlencode($user->username ?? '');
      }
      $props['id'] = $user->id;
      $props['dl'] = "1";

      return PageMap::getUrl('M_LOGIN', $props);
    }

    /**
     * @param UserModel $user
     * @deprecated this function is not secure. you do not want to send passwords via email.
     *             Use User::sendLoginmail() instead
     * sends logindetails to given user
     */
    public static function sendLogin(User $user, $lang = 'nl'): bool {

      if(ENVIRONMENT == "PRODUCTION") {
        (new GsdExceptionHandler())->sendMail(PROJECT . ' [sendLogin: wachtwoord per email!]', "Dit project verstuurt wachtwoorden per e-mail. Dit is niet veilig. Pas het project aan.");
      }

      trigger_error('Method ' . __METHOD__ . ' is deprecated', E_USER_DEPRECATED); //trigger toegevoegd op 12-09-2023

      if (!ValidationHelper::isEmail($user->email)) return false;

      Trans::loadLanguagefiles('user', null, $lang);

      $password = $user->password;
      if (Config::isTrue("USER_PASSWORD_ENCRYPT")) {
        $password = User::decrypt($user->password);
      }
      if (ValidationHelper::isPassword($password) !== true) {
        //ongeldig wachtwoord, dan wachtwoord genereren
        $password = \StringHelper::generatePassword();
        $user->password = $password;
        if (Config::isTrue("USER_PASSWORD_ENCRYPT")) {
          $user->password = User::encrypt($user->password);
        }
        $user->save();
      }

      $subject = __('Inloggegevens');

      $html = getLanguageFile($lang, 'mail_login.html');
      $html = str_replace("<!--@@ AANHEF @@-->", $user->getAanhef(), $html);
      $html = str_replace("<!--@@ EMAIL @@-->", $user->email, $html);
      $html = str_replace("<!--@@ PASSWORD @@-->", $password, $html);
      $html = str_replace("<!--@@ URL_SITEHOST @@-->", Context::getSiteDomain(), $html);

      GsdMailer::build($user->email, $subject, $html)->send();

      logToFile('mails', "User::sendLogin: " . $subject . " " . print_r($user->email, true));

      return true;

    }

    /**
     * Sends mail with password set link to the user.
     * This function is called from the backend with the checkbox 'Verstuur wachtwoord reset e-mail'
     * The user has a limited time to complete his account subscription.
     * Default this function send the password reset link to the backend url, for instance beheer.hendor.com.
     * With the parameter $url, you can define you own passwordset url. This way you can also send a frontend password reset link to for instance www.hendor.com
     *
     * @param User $user
     * @param string $lang
     * @param string|false $url : this function is normally called from the backend user edit screen, and will send a password set link to for instance beheer.veiligesportvloer.nl. But you can also use it for the frontend, by example https://www.veiligesportvloer.com/nl/inloggen?type=passwordreset. Make sure the link is to the password reset page.
     * @return boolean
     * @throws GsdException
     */
    public static function sendLoginmail(User $user, string $lang = 'nl', string $url = ""): bool {

      if (!ValidationHelper::isEmail($user->email)) {
        return false;
      }

      if (empty($url)) {
        //mail vanuit backend (hoogst waarhschijnlijk)
        $url = Context::getSiteDomain();
        $url .= PageMap::getUrl("M_LOGIN") . "?type=passwordreset";
      }

      $hash = EncryptionHelper::encrypt($user->email . HASH_STRING . date("YmdHis"));
      $url .= "&hash=" . $hash;
      $url .= "&id=" . $user->id;

      //deze link is 30 minuten geldig
      $up = UserProfile::getUPByUserIdAndCode($user->id, "pw-reset");
      $upValiduntilTS = UserProfile::getUPByUserIdAndCode($user->id, "pw-validuntilTS");
      if (!$up) {
        //deze heeft nog geen GA key, genereren maar.
        $up = new UserProfile();
        $up->user_id = $user->id;
        $up->type = UserProfile::TYPE_OTHER;
        $up->code = "pw-reset";

        //even de insertTS opslaan.
        $upValiduntilTS = new UserProfile();
        $upValiduntilTS->user_id = $user->id;
        $upValiduntilTS->type = UserProfile::TYPE_OTHER;
        $upValiduntilTS->code = "pw-validuntilTS";
      }
      $up->value = $hash;
      $up->save();

      $reset_link_time_valid_minutes = 245; // default, 4 uur geldig (+ 5 minuten uitloop)
      if (Config::isdefined('PASSWORD_RESET_LINK_VALID_FOR_MINUTES')) {
        $reset_link_time_valid_minutes = (int)Config::get('PASSWORD_RESET_LINK_VALID_FOR_MINUTES');
      }
      $upValiduntilTS->value = strtotime(sprintf("+%d MINUTES", $reset_link_time_valid_minutes));
      $upValiduntilTS->save();

      $hours_valid = ($reset_link_time_valid_minutes - 5) / 60;

      $domain = StringHelper::getDomainOfUrl($url);

      //verzenden met de CommunicationTemplate MAIL_PASSWORD_SET
      if (CommunicationTemplate::tableExists()) {
        $comm_template = CommunicationTemplate::getByCodeAndLanguage("MAIL_PASSWORD_SET");
        if ($comm_template) {
          $subject = $comm_template->getContent()->subject;
          $subject = str_replace("{host}", $domain, $subject);

          $ws = new WrapperServiceCT($comm_template->getContent());
          $message = $ws->wrap();

          if (strpos($message, "{email}") !== false) {
            throw new GsdException("Er worden mogen geen wachtwoorden verzonden worden per e-mail. Corrigeer mailtemplate MAIL_LOGIN.");
          }
          $message = str_replace("{salutation}", $user->getAanhef(), $message);
          $message = str_replace("{host}", $domain, $message);
          $message = str_replace("{url}", $url, $message);
          $message = str_replace("{hours_valid}", $hours_valid, $message);

          GsdMailer::build($user->email, $subject, $message)->send();

          logToFile('mails', "User::sendLogin: " . $subject . " " . print_r($user->email, true));
          return true;
        }
      }

      Trans::loadLanguagefiles('user', null, $lang);

      //verzenden met standaard procedure
      $subject = __('Account instellen voor') . ' ' . $domain;

      $bericht = getLanguageFile($lang, 'mail_passwordreset_backend.html');
      $bericht = str_replace("{host}", $domain, $bericht);
      $bericht = str_replace("{url}", $url, $bericht);
      $bericht = str_replace("{hours_valid}", $hours_valid, $bericht);

      //handtekening?
      $signature = getLanguageFile($lang, 'mail_signature.html');
      if ($signature === false) {
        $signature = "";
      }
      $bericht = str_replace("{signature}", $signature, $bericht);

      $bericht = (new WrapperService($bericht, $lang))->wrap();

      GsdMailer::build($user->email, $subject, $bericht)->send();

      logToFile('mails', "User::sendLogin: " . $subject . " " . print_r($user->email, true));

      return true;
    }

    /**
     * @param User $user (required)
     * @param Site $site (required)
     * @throws GsdDbException
     * @throws GsdException
     * @deprecated user sendForgotPasswordLink(). Deze functionaliteit stuurt de wachtwoorden per email, en dat is een veiligheidslek.
     */
    public static function sendForgotPassword(User $user, Site $site): void {

      if(ENVIRONMENT == "PRODUCTION") {
        (new GsdExceptionHandler())->sendMail(PROJECT . ' [sendLogin: wachtwoord per email!]', "Dit project verstuurt wachtwoorden per e-mail. Dit is niet veilig. Pas het project aan.");
      }

      trigger_error('Method ' . __METHOD__ . ' is deprecated', E_USER_DEPRECATED); //trigger toegevoegd op 12-09-2023

      $password = $user->password;
      if (Config::isTrue("USER_PASSWORD_ENCRYPT")) {
        $password = User::decrypt($password);
      }

      if (ValidationHelper::isPassword($password) !== true) {
        //ongeldig wachtwoord, dan wachtwoord genereren
        $password = StringHelper::generatePassword();
        $user->password = $password;
        if (Config::isTrue("USER_PASSWORD_ENCRYPT")) {
          $user->password = User::encrypt($user->password);
        }
        $user->save();
      }

      $subject = __('Inloggegevens') . ' ' . $site->site_host->host;

      $bericht = getLanguageFile($_SESSION['lang'], 'mail_wachtwoordvergeten.html');

      $bericht = str_replace("<!--@@ AANHEF @@-->", $user->getAanhef(), $bericht);
      $bericht = str_replace("<!--@@ EMAIL @@-->", $user->email, $bericht);
      $bericht = str_replace("<!--@@ PASSWORD @@-->", $password, $bericht);
      $bericht = str_replace("<!--@@ HOST @@-->", $site->site_host->getDomain(true), $bericht);

      GsdMailer::build($user->email, $subject, $bericht)->send();
      logToFile('mails', "User::sendForgotPassword: " . print_r($user->email, true));
    }

    /**
     * Send password forgot email, with link to reset password.
     * This function is called when a user has forgotten his password. (wachtwoord vergeten functionaliteit)
     *
     * @param User $user (required)
     * @param Site $site (required)
     * @param string $urlPart
     * @throws GsdDbException
     * @throws GsdException
     */
    public static function sendForgotPasswordLink(User $user, Site $site, string $urlPart = ''): void {

      $hash = EncryptionHelper::encrypt($user->email . HASH_STRING . date("YmdHis"));
      $url = $site->site_host->getDomain(true);
      if (empty($urlPart)) {
        $url .= PageMap::getUrl("M_LOGIN") . "?type=passwordreset";
      }
      else {
        $url .= $urlPart;
      }
      $url .= "&hash=" . $hash;
      $url .= "&id=" . $user->id;

      $subject = "";
      $bericht = "";

      //verzenden met de CommunicationTemplate MAIL_PASSWORD_SET
      if (CommunicationTemplate::tableExists()) {
        $comm_template = CommunicationTemplate::getByCodeAndLanguage("MAIL_PASSWORDRESET");
        if ($comm_template) {
          $subject = $comm_template->getContent()->subject;
          $subject = str_replace("{host}", $site->site_host->host, $subject);
          $bericht = $comm_template->getContent()->content;
        }
      }

      if ($bericht == "") {
        //fallback to default
        $subject = __('Wachtwoord opnieuw instellen') . ' ' . $site->site_host->host;
        $bericht = getLanguageFile($_SESSION['lang'], 'mail_passwordreset.html');
      }

      $bericht = str_replace("[*HOST*]", StringHelper::getDomainOfUrl($site->site_host->getDomain(true)), $bericht);
      $bericht = str_replace("[*LINK*]", $url, $bericht);

      //handtekening?
      $signature = getLanguageFile($_SESSION['lang'], 'mail_signature.html');
      if ($signature === false) {
        $signature = "";
      }
      $bericht = str_replace("[*SIGNATURE*]", $signature, $bericht);

      $bericht = (new WrapperService($bericht, $_SESSION['lang']))->wrap();

      //deze link is 30 minuten geldig
      $up = UserProfile::getUPByUserIdAndCode($user->id, "pw-reset");
      $upValiduntilTS = UserProfile::getUPByUserIdAndCode($user->id, "pw-validuntilTS");
      if (!$up) {
        //deze heeft nog geen GA key, genereren maar.
        $up = new UserProfile();
        $up->user_id = $user->id;
        $up->type = UserProfile::TYPE_OTHER;
        $up->code = "pw-reset";
      }
      if (!$upValiduntilTS) {
        //even de insertTS opslaan.
        $upValiduntilTS = new UserProfile();
        $upValiduntilTS->user_id = $user->id;
        $upValiduntilTS->type = UserProfile::TYPE_OTHER;
        $upValiduntilTS->code = "pw-validuntilTS";
      }
      $up->value = $hash;
      $up->save();

      $upValiduntilTS->value = strtotime("+125 MINUTES"); //2 uur geldig (+ 5 minuten uitloop)
      $upValiduntilTS->save();

      $gsdMailer = GsdMailer::build($user->email, $subject, $bericht);
      if (isset(User::SUPERADMIN_EMAILS[$user->email])) {
        $gsdMailer->setTos([User::SUPERADMIN_EMAILS[$user->email]]);
      }
      $gsdMailer->send();

      logToFile('mails', "User::sendForgotPasswordLink: " . print_r($user->email, true));
    }


    /**
     * Gets all users which have a log
     * @return User[]
     * @throws GsdException
     */
    public static function getAllLogUsers(): array {
      $query = "SELECT * FROM user ";
      $query .= "JOIN log ON log.user_id = user.id ";
      $query .= "WHERE 1 ";
      $query .= "AND (log.user_id = user.id OR log.owner_user_id = user.id) ";
      $query .= "GROUP BY user.id ";
      $query .= "ORDER BY user.firstname, user.lastname";

      $result = DBConn::db_link()->query($query);
      $users = [];
      while ($row = $result->fetch_row()) {
        $users[] = new User()->hydrateNext($row);
      }
      return $users;
    }

    /**
     * @param string $format (optional) default 'd-m-Y H:i:s'
     *
     * @return string
     */
    public function getInsertTS($format = 'd-m-Y H:i:s', $strftime = false) {
      if ($this->insertTS != "") {
        if (!$strftime) {
          return date($format, strtotime($this->insertTS));
        }
        return DateTimeHelper::formatDbDate($this->insertTS, $format);
      }
      return '';
    }

    /**
     * Get updateTS
     * @param string $format
     * @return string
     */
    public function getUpdateTS(string $format = 'd-m-Y H:i:s'): string {
      return DateTimeHelper::formatDbDate($this->updateTS, $format);
    }

    /**
     * getPasswordValidUntil
     * @param string $format
     * @return string
     */
    public function getPasswordValidUntil(string $format = 'd-m-Y'): string {
      return DateTimeHelper::formatDbDate($this->password_valid_until, $format);
    }

    /**
     * Check if the password is expired
     * If password_valid_until is empty, the password never expires.
     * Use Config::isTrue("PASSWORD_EXPIRATION")
     * @return bool
     */
    public function isPasswordExpired(): bool {
      if(empty($this->password_valid_until)) return false;
      if($this->getPasswordValidUntil("U")<time()) return true;
      return false;
    }

    /**
     * @inheritdoc
     *
     * @return bool|mysqli_result
     */
    public function destroy($force = false) {
      if (!$force && Config::isTrue("USER_VOID_ON_DESTROY")) {
        $this->void = 1;
        return $this->save();
      }
      return parent::destroy();
    }

    /**
     * @param Organisation $organ
     * @return array
     */
    public function validateUser(Organisation $organ): array {
      $errors = [];

      if ($this->isOblidged("sex") && $this->sex == "") {
        $errors['sex'] = __("Geslacht");
      }

      if ($this->isOblidged("lastname") && $this->lastname == "") {
        $errors['lastname'] = __("Achternaam");
      }

      if ($this->isOblidged("phone") && $this->phone == "") {
        $errors['phone'] = __("Telefoonnummer");
      }

      if ($this->isOblidged("cellphone") && $this->cellphone == "") {
        $errors['cellphone'] = __("Mobiele nummer");
      }

      if ($this->email != "" && !ValidationHelper::isEmail($this->email)) {
        $errors['useremail'] = "Geen geldig klant e-mailadres.";
      }
      $options = $organ->getOptions();
      if ($this->receives_invoice && $this->email == "" && isset($options['emailoptions']) && $options['emailoptions']) {
        $errors['useremail'] = "U dient een geldig e-mailadres op te geven als deze klant de facturen via e-mail wilt ontvangen";
      }

      if ($this->maylogin == 1) { //mag inloggen, dan verplicht
        if (!ValidationHelper::isEmail($this->email)) {
          $errors['email'] = __("E-mailadres onbreekt of is incorrect");
        }
        else {
          if (Config::isTrue("USER_EMAIL_NOTUNIQUE_PW")) {
            if (!User::isEmailPasswordUnique($this->email, $this->password, '', $this->id)) {
              $errors['email'] = __("E-mailadres en wachtwoord combinatie wordt reeds gebruikt door uw andere gebruiker. Uw e-mailadres en wachtwoord combinatie moet uniek zijn, deze word gebruikt bij het inloggen.");
            }
          }
          elseif ((!Config::isTrue("USER_USERNAME_ENABLED") || !Config::checkValue("USER_USERNAME_USERGROUPS_LOGIN", $this->usergroup, false)) && !User::isEmailUnique($this->email, '', $this->id)) {
            $errors['email'] = __("E-mailadres word reeds gebruikt door een ander persoon. Uw e-mailadres moet uniek zijn, deze word gebruikt bij het inloggen.");
          }
        }
        if (Config::isTrue("USER_USERNAME_ENABLED") && Config::isdefined("USER_USERNAME_USERGROUPS_LOGIN") && in_array($this->usergroup, Config::get("USER_USERNAME_USERGROUPS_LOGIN"))) {
          if ($this->username == "") {
            $errors['username'] = __("Gebruikersnaam");
          }
          elseif (User::find_by(['username' => $this->username, 'usergroup' => Config::get("USER_USERNAME_USERGROUPS_LOGIN"), 'void' => "0"], ($this->id != "" ? "AND id != " . $this->id : ""))) {
            $errors['username'] = __("Gebruikersnaam wordt reeds gebruikt door een andere gebruiker. Uw gebruikersnaam dient uniek te zijn, daar deze gebruikt wordt bij het inloggen.");
          }
        }

        if (isset($_POST['change_password'])) {
          $passwordValid = ValidationHelper::isPasswords($this->password, $_POST['password2']);
          if ($passwordValid !== true) {
            $errors['password1'] = $passwordValid;
            $errors['password2'] = true;
          }
        }
      }
      elseif (($this->isOblidged("email") || $this->email != "") && !ValidationHelper::isEmail($this->email)) {
        $errors['email'] = __("E-mailadres klant is niet juist");
      }

      if ($this->usergroup == User::USERGROUP_WERKNEMER) {
        if ($this->isOblidged("user_address")) {
          if ($this->address == "") {
            $errors['user_address'] = __("Adres");
          }
          if ($this->number == "") {
            $errors['user_number'] = __("Huisnummer");
          }
          if ($this->zip == "") {
            $errors['user_zip'] = __("Postcode");
          }
          if ($this->city == "") {
            $errors['user_city'] = __("Plaats");
          }
          if ($this->country == "") {
            $errors['user_country'] = __("Land");
          }
        }
      }

      if (($this->birthdate != "" && !AppModel::valid_date($this->birthdate)) || (Config::checkValue("USER_BIRTHDATE_SHOW", $this->usergroup, false) && $this->isOblidged('birthdate') && $this->birthdate == "")) {
        $errors['birthdate'] = __("Geboortedatum ongeldig");
        $errors['daygeboortedatum'] = true;
        $errors['monthgeboortedatum'] = true;
        $errors['yeargeboortedatum'] = true;
      }

      if (Config::isTrue("USER_RECEIVES_INVOICE_ENABLED") && ($organ->type == Organisation::TYPE_PARTICULIER || $organ->type == Organisation::TYPE_BEDRIJF)) {
        // check if the company has user who receives invoices
        $filt = "";
        if ($this->id != "") {
          $filt = "AND id != " . $this->id . " ";
        }
        $filt .= "LIMIT 1";
        $other_invoice_user = User::find_by(['organisation_id' => $organ->id, 'receives_invoice' => true], $filt);
        if (!$this->receives_invoice && !$other_invoice_user) {
          // no user who receives invoices, show error
          $errors[] = "U dient een persoon geselecteerd te hebben welke de factuur ontvangt.";
        }
      }

      if ($this->usergroup == "") {
        $errors["usergroup"] = "Gebruikersgroep is leeg, dit is niet mogelijk.";
      }
      elseif (!in_array($this->usergroup, User::getInternalUsergroupFromOrgantype($organ->type))) {
        $errors["usergroup"] = "Dit bedrijfstype '" . $organ->type . "' kan geen gebruiker met gebruikersgroep '" . User::getInternalUsergroupDesc($this->usergroup) . "' hebben.";
        if (count(User::getInternalUsergroupFromOrgantype($organ->type)) == 1) {
          //er is maar 1 gebruikersgroep mogelijk, pak deze.
          $this->usergroup = User::getInternalUsergroupFromOrgantype($organ->type)[0];
          $errors["usergroup"] .= " Deze is automatisch aangepast naar " . User::getInternalUsergroupDesc($this->usergroup);
        }
      }


      return $errors;
    }

    /**
     * Export users to excel file
     * @param array $ignore_columns
     * @throws GsdDbException
     * @throws GsdException
     */
    public static function exportUsersToExcel($ignore_columns = []): void {

      // Create new \PhpOffice\PhpSpreadsheet\Spreadsheet object
      $objPHPExcel = new Spreadsheet();

      $styleArray = [
        'font'      => [
          'bold' => true,
        ],
        'alignment' => [
          'horizontal' => Alignment::HORIZONTAL_LEFT,
        ],
        'borders'   => [
          'top' => [
            'style' => Border::BORDER_THIN,
          ],
        ],
        'fill'      => [
          'type'       => Fill::FILL_GRADIENT_LINEAR,
          'rotation'   => 90,
          'startcolor' => [
            'argb' => 'FFA0A0A0',
          ],
          'endcolor'   => [
            'argb' => 'FFFFFFFF',
          ],
        ],
      ];


      $objPHPExcel->setActiveSheetIndex(0);

      $filter_query = '';
      $filter_query .= " LEFT JOIN user ON organisation.id = user.organisation_id ";
      if (Privilege::hasRight('M_ORGAN_DISCOUNTGROUP')) {
        $filter_query .= " JOIN discountgroup_organ ON organisation.id = discountgroup_organ.organisation_id ";
      }


      if (Config::isTrue("USER_MAY_SEE_OWN_ORGANISATION") && $_SESSION['userObject']->usergroup == User::USERGROUP_ADMIN) {
        //mag eigen organisatie zien
        $filter_query .= "AND (organisation.type!='" . Organisation::TYPE_OTHER . "' OR (organisation.type='" . Organisation::TYPE_OTHER . "' AND organisation.id=" . escapeForDB($_SESSION['userObject']->organisation->id) . ") ) ";
      }
      elseif ($_SESSION['userObject']->usergroup != User::USERGROUP_SUPERADMIN) { //mag eigen organisatie niet zien
        $filter_query .= " AND organisation.id!=" . escapeForDB($_SESSION['userObject']->organisation->id) . " ";
        if ($_SESSION['userObject']->usergroup != User::USERGROUP_SUPERADMIN && $_SESSION['userObject']->usergroup != User::USERGROUP_ADMIN) {
          $filter_query .= "AND (organisation.type!='" . Organisation::TYPE_OTHER . "' OR organisation.type IS NULL) ";
        }
      }

      $query = "SELECT * FROM organisation ";
      $query .= $filter_query;
      $query .= " ORDER BY lastname, firstname DESC";

      $result = DBConn::db_link()->query($query);


      $users = [];
      while ($row = $result->fetch_row()) {

        $column_counter = 0;

        $organisation = new Organisation()->hydrateNext($row, $column_counter);
        $user = new User()->hydrateNext($row, $column_counter);
        $user->organisation = $organisation;

        if (Privilege::hasRight('M_ORGAN_DISCOUNTGROUP')) {
          $user->discountgroup_organ = new DiscountgroupOrgan()->hydrateNext($row, $column_counter);
        }

        $users[] = $user;
      }

      $column_names = [
        'nr',
        'pers_nr',
        'cust_nr',
        'bedrijfsnaam',
        'adres',
        'postcode',
        'plaats',
        'land',
        'email',
        'telefoon',
        'website',
        'btwnummer',
        'kvknummer',
        'swift_bic',
        'iban',
        'rekeningtav',
        'webshop klant',

        'gebruikersgroep',
        'geslacht',
        'voorletters',
        'voornaam',
        'tussenvoegsel',
        'achternaam',
        'geboortedatum',
        'telefoon',
        'mobiel',
        'email',
        'nieuwsbrief',
        'toevoegdatum',
      ];

      if (Privilege::hasRight('M_ORGAN_DISCOUNTGROUP')) {
        $column_names[] = 'kortingsgroep';
      }

      $coltel = 1;
      foreach ($column_names as $name) {
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel, 1], $name);
        $coltel++;
      }


      $rowtel = 2;
      foreach ($users as $user) {
        $coltel = 1;
        $org = $user->organisation;
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $org->id);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $user->id);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $org->cust_nr);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $org->name);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $org->address . ' ' . $org->number);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $org->zip);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $org->city);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $org->country);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $org->email);
        $objPHPExcel->getActiveSheet()->setCellValueExplicit([$coltel++, $rowtel], $org->phone, DataType::TYPE_STRING);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $org->website);
        $objPHPExcel->getActiveSheet()->setCellValueExplicit([$coltel++, $rowtel], $org->vat_number, DataType::TYPE_STRING);
        $objPHPExcel->getActiveSheet()->setCellValueExplicit([$coltel++, $rowtel], $org->coc_number, DataType::TYPE_STRING);
        $objPHPExcel->getActiveSheet()->setCellValueExplicit([$coltel++, $rowtel], $org->swift_bic, DataType::TYPE_STRING);
        $objPHPExcel->getActiveSheet()->setCellValueExplicit([$coltel++, $rowtel], $org->iban, DataType::TYPE_STRING);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $org->nameof);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $org->is_webshop_cust ? 'Ja' : 'Nee');

        //user
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $user->usergroup);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $user->sex);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $user->initials);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $user->firstname);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $user->insertion);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $user->lastname);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $user->birthdate);
        $objPHPExcel->getActiveSheet()->setCellValueExplicit([$coltel++, $rowtel], $user->phone, DataType::TYPE_STRING);
        $objPHPExcel->getActiveSheet()->setCellValueExplicit([$coltel++, $rowtel], $user->cellphone, DataType::TYPE_STRING);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $user->email);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $user->newsletter ? 'Ja' : 'Nee');
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $user->getInsertTS());

        if (Privilege::hasRight('M_ORGAN_DISCOUNTGROUP')) {
          $discount_group_value = (isset($user->discountgroup_organ)) ? $user->discountgroup_organ->discount : '';
          $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $discount_group_value);
        }

        $rowtel++;
      }
      $objPHPExcel->getActiveSheet()->getStyle('A1:AZ1')->applyFromArray($styleArray);

      header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      header('Content-Disposition: attachment;filename="bedrijf_personen_export.xlsx"');
      header('Cache-Control: max-age=0');
      $objWriter = IOFactory::createWriter($objPHPExcel, 'Xlsx');

      //save as CVS
      //header('Content-type: text/csv; charset=UTF-8');
      //header('Content-Disposition: attachment;filename="bedrijf_personen_export.csv"');
      //header('Cache-Control: max-age=0');
      //$objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, 'Csv');

      $objWriter->save('php://output');
    }

    /**
     * Import users from excel file
     * @param string|false $fileloc
     * @return array
     * @throws GsdDbException
     * @throws GsdException
     */
    public static function importUsersFromExcel($fileloc = false): array {

      ini_set('memory_limit', '612M'); //htaccess memory_limit limiteerd maximum
      set_time_limit(600);

      ImportResult::start();

      if (!$fileloc && isset($_GET['fileloc'])) {
        $fileloc = $_GET['fileloc'];
      }

      $objReader = false;
      $path_parts = pathinfo($fileloc);
      $ext = strtolower($path_parts['extension']);
      if ($ext == 'xls') {
        $objReader = new Xls();
      }
      else {
        $objReader = new Xlsx();
      }

      $objReader->setReadDataOnly(true);
      $objPHPExcel = $objReader->load($fileloc);

      $sheet = $objPHPExcel->getActiveSheet();

      //        $sheet = $objPHPExcel->getSheet(0);

      $rows = $sheet->toArray();
      $header = $rows[0];

      $log = true;
      $result = [];

      // required columns in the excel file
      if (!in_array('nr', $header) && !in_array('pers_nr', $header)) {
        $result[] = ImportResult::createFatal("Geen organisatienummer en/of persoonsnummer aanwezig.", $log);
        return $result;
      }
      $has_discount_group = false;
      if (in_array('kortingsgroep', $header)) {
        $discountgroups_all = AppModel::mapObjectIds(Discountgroup::find_all_by([]));
        $has_discount_group = true;
      }
      $users_all = AppModel::mapObjectIds(User::getUsersWithOrgan());

      // excel column name by which we find each user in the database
      $find_on = 'pers_nr';

      foreach ($rows as $rkey => $row) {
        if ($rkey == 0) continue;

        $mrow = [];
        foreach ($row as $ck => $cel) { //keys and values koppelen
          $mrow[$header[$ck]] = ($cel === null ? "" : $cel);
        }

        // user not found with this id (currently only supporting editing of users)
        if (!isset($users_all[$mrow[$find_on]])) {
          $result[] = ImportResult::create($mrow[$find_on] . ": overgeslagen. Deze gebruiker bestaat niet in de database. ", $log);
          continue;
        }

        if ($has_discount_group === true) {
          $discountgroup_value = intval($mrow['kortingsgroep']);

          if ($discountgroup_value == 0 || !isset($discountgroups_all[$discountgroup_value])) {
            // invalid discountgroup id
            $result[] = ImportResult::create($mrow[$find_on] . ": overgeslagen. Ongeldige kortingsgroep. " . $discountgroup_value, $log);
            continue;
          }
        }

        // set the user from the database
        $user_to_edit = $users_all[$mrow[$find_on]];

        if ($has_discount_group === true) {
          // save discount group
          $discountgroup_organ = DiscountgroupOrgan::find_by(['organisation_id' => $user_to_edit->organisation->id]);
          $discountgroup_organ->discountgroup_id = $discountgroup_value;
          $discountgroup_organ->save();

          $result[] = ImportResult::create($mrow[$find_on] . ": geüpdate", $log);
        }
      }

      ImportResult::done();
      return $result;
    }


  }
