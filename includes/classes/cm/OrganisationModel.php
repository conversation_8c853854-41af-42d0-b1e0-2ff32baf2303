<?php

  AppModel::loadBaseClass('BaseOrganisation');

  class OrganisationModel extends BaseOrganisation {

    const TYPE_DISTRIBUTEUR = 'DISTRIBUTEUR';
    const TYPE_AGENT = 'AGENT';
    const TYPE_KAPSALON = 'KAPSALON';
    const TYPE_PARTICULIER = 'PARTICULIER';
    const TYPE_OTHER = 'OTHER';
    const TYPE_BEDRIJF = 'BEDRIJF';
    const TYPE_SHOP = 'SHOP';
    const TYPE_HOVENIER = 'HOVENIER';
    const TYPE_DEALER = 'DEALER';
    const TYPE_SUBDEALER = 'SUBDEALER';
    const TYPE_LEVERANCIER = 'LEVERANCIER';
    const TYPE_MAGAZIJN = 'MAGAZIJN';
    const TYPE_ONDERAANNEMER = 'ONDERAANNEMER';
    const TYPE_FABRIEKSPLANNER = 'FABRIEKSPLANNER';
    const TYPE_ACCOUNTMANAGER = 'ACCOUNTMANAGER';
    const TYPE_SCHOOL = 'SCHOOL';
    const TYPE_KINDERDAGVERBLIJF = 'KINDERDAGVERBLIJF';

    //JAM
    const TYPE_FRANCHISEE = 'FRANCHISEE';
    const TYPE_JONGERE = 'JONGERE';
    const TYPE_STARTER = 'STARTER';
    const TYPE_WEBSITE_MANAGER = 'WEBSITE_MANAGER';

    //Blue-merge
    const TYPE_SCHOOLS_COMPANY = 'SCHOOLS_COMPANY';
    const TYPE_WHITELABEL = 'WHITELABEL';

    const array ORGANISATION_TYPES = [
      self::TYPE_DISTRIBUTEUR      => "Distributeur",
      self::TYPE_AGENT             => "Agent",
      self::TYPE_KAPSALON          => "Salon",
      self::TYPE_PARTICULIER       => "Particulier",
      self::TYPE_OTHER             => "Overig",
      self::TYPE_BEDRIJF           => "Bedrijf",
      self::TYPE_SHOP              => "Shop", //hovenier shop
      self::TYPE_HOVENIER          => "Hovenier",
      self::TYPE_DEALER            => "Dealer",
      self::TYPE_SUBDEALER         => "Subdealer",
      self::TYPE_LEVERANCIER       => "Leverancier", //let op deze gebruiker heeft eigen pagina
      self::TYPE_MAGAZIJN          => "Magazijn",
      self::TYPE_ONDERAANNEMER     => "Onderaannemer",
      self::TYPE_FABRIEKSPLANNER   => "Fabrieksplannner",
      self::TYPE_ACCOUNTMANAGER    => "Accountmanager",
      self::TYPE_SCHOOL            => "Onderwijsinstelling",
      self::TYPE_KINDERDAGVERBLIJF => "Kinderdagverblijf",
      self::TYPE_SCHOOLS_COMPANY   => "Dummybedrijf",
      self::TYPE_WHITELABEL        => "Whitelabel",
    ];

    public function hasSocialmedia(): bool {
      if (OrganisationProfile::hasSocial($this->id)) {
        return true;
      }
      return false;
    }

    public static function getFilterQuery(): string {
      $filtquery = "";
      $filtquery .= " LEFT JOIN user ON organisation.id = user.organisation_id ";

      if (isset($_SESSION['s_device']) && $_SESSION['s_device'] == 1) {
        $filtquery .= " LEFT JOIN cvinfo ON cvinfo.organisation_id = user.organisation_id ";
        $filtquery .= " LEFT JOIN cvkenmerken ON cvkenmerken.cvinfoid = cvinfo.id ";
      }

      $filtquery .= " WHERE 1 ";
      if (isset($_SESSION['organtypes_toshow']) && count($_SESSION['organtypes_toshow']) > 0) {
        $filtquery .= " AND organisation.type IN ('" . implode(',', $_SESSION['organtypes_toshow']) . "') ";
      }
      if (isset($_SESSION['organtypes_tohide']) && count($_SESSION['organtypes_tohide']) > 0) {
        $filtquery .= " AND NOT organisation.type IN ('" . implode(',', $_SESSION['organtypes_tohide']) . "') ";
      }
      if (!empty($_SESSION['s_search'])) {
        $searchval = DbHelper::escape($_SESSION['s_search']);
        $filtquery .= " AND (";
        $filtquery .= " user.firstname LIKE '%" . $searchval . "%'";
        $filtquery .= " OR CONCAT(user.firstname, ' ', user.lastname) LIKE '%" . $searchval . "%'";
        $filtquery .= " OR CONCAT(user.firstname, ' ', user.insertion, ' ', user.lastname) LIKE '%" . $searchval . "%'";
        $filtquery .= " OR user.initials LIKE '%" . $searchval . "%'";
        $filtquery .= " OR user.lastname LIKE '%" . $searchval . "%'";
        $filtquery .= " OR user.email LIKE '%" . $searchval . "%'";
        $filtquery .= " OR user.phone LIKE '%" . $searchval . "%'";
        $filtquery .= " OR user.cellphone LIKE '%" . $searchval . "%'";
        $filtquery .= " OR user.department LIKE '%" . $searchval . "%'";

        $filtquery .= " OR organisation.cust_nr = '" . $searchval . "'";
        $filtquery .= " OR organisation.address LIKE '%" . $searchval . "%'";
        $filtquery .= " OR organisation.number LIKE '%" . $searchval . "%'";
        $filtquery .= " OR organisation.zip LIKE '%" . $searchval . "%'";
        $filtquery .= " OR organisation.city LIKE '%" . $searchval . "%'";

        $filtquery .= " OR organisation.name LIKE '%" . $searchval . "%'";
        $filtquery .= " OR organisation.phone LIKE '%" . $searchval . "%'";
        $filtquery .= " OR organisation.vat_number LIKE '%" . $searchval . "%'";
        $filtquery .= " OR organisation.coc_number LIKE '%" . $searchval . "%'";
        $filtquery .= " OR organisation.email_invoice LIKE '%" . $searchval . "%'";
        if (is_numeric($searchval)) {
          $filtquery .= " OR organisation.id = '" . $searchval . "'";
        }

        if (isset($_SESSION['s_device']) && $_SESSION['s_device'] == 1) {
          $filtquery .= " OR cvkenmerken.keydescription LIKE '%" . $searchval . "%' ";
        }

        $filtquery .= " )";
      }

      if (!empty($_SESSION['firstletter'])) {
        $filtquery .= " AND user.lastname LIKE '" . DbHelper::escape($_SESSION['firstletter']) . "%' ";
      }
      if (!empty($_SESSION['firstletter_company'])) {
        $filtquery .= " AND organisation.name LIKE '" . DbHelper::escape($_SESSION['firstletter_company']) . "%' ";
      }

      if (isset($_SESSION['s_country']) && $_SESSION['s_country'] !== "") {
        $filtquery .= "AND organisation.country = '" . DbHelper::escape($_SESSION['s_country']) . "'";
      }

      if (isset($_SESSION['s_province']) && $_SESSION['s_province'] !== "") {
        $filtquery .= "AND organisation.province_id = '" . DbHelper::escape($_SESSION['s_province']) . "'";
      }

      if (Config::isTrue("USER_VOID_ON_DESTROY")) {
        $filtquery .= " AND (user.void = 0 OR user.id IS NULL) ";
      }

      if (isset($_SESSION['s_inactive']) && $_SESSION['s_inactive'] !== "") {
        $filtquery .= " AND organisation.active = " . escapeForDB($_SESSION['s_inactive']) . " ";
        $filtquery .= " AND (user.active = " . escapeForDB($_SESSION['s_inactive']) . " OR user.id IS NULL) ";
      }

      if (isset($_SESSION['s_direct_login']) && $_SESSION['s_direct_login'] !== "") {
        $filtquery .= " AND user.maylogin = " . escapeForDB($_SESSION['s_direct_login']) . " ";
      }

      if (isset($_SESSION['s_is_webshop_cust']) && $_SESSION['s_is_webshop_cust'] !== "") {
        $filtquery .= " AND organisation.is_webshop_cust = " . escapeForDB($_SESSION['s_is_webshop_cust']) . " ";
      }

      if (Config::isTrue("ORGANISATION_PROSPECT_ENABLED")) {
        if (isset($_SESSION['s_prospect']) && $_SESSION['s_prospect'] !== "") {
          $filtquery .= " AND organisation.is_prospect = " . escapeForDB($_SESSION['s_prospect']) . " ";
        }
      }

      if (isset($_SESSION['s_usergroup']) && $_SESSION['s_usergroup'] != "Alles" && $_SESSION['s_usergroup'] != '') {
        $filtquery .= "AND user.usergroup = '" . $_SESSION['s_usergroup'] . "' ";
      }

      if (!isset($_SESSION['userObject']) || $_SESSION['userObject']->usergroup != User::USERGROUP_SUPERADMIN) { //alleen superadmin (ik) mag superadmin zien
        $filtquery .= " AND (user.usergroup != '" . User::USERGROUP_SUPERADMIN . "' OR user.usergroup IS NULL)  ";
      }

      if (isset($_SESSION['userObject']) && $_SESSION['userObject']->usergroup == User::USERGROUP_FRANCHISEE) {
        $filtquery .= " AND owner_organisation_id= " . $_SESSION['userObject']->organisation->id . " ";
      }

      if (Config::isTrue("USER_ADMIN_MAY_SEE_ALL") && $_SESSION['userObject']->usergroup == User::USERGROUP_ADMIN) {
        //mag alles zien behalve superadmin
      }
      elseif (Config::isTrue("USER_MAY_SEE_OWN_ORGANISATION") && $_SESSION['userObject']->usergroup == User::USERGROUP_ADMIN) {
        //mag eigen organisatie ook zien
        $filtquery .= "AND (organisation.type!='" . Organisation::TYPE_OTHER . "' OR (organisation.type='" . Organisation::TYPE_OTHER . "' AND organisation.id=" . $_SESSION['userObject']->organisation->id . ") ) ";
      }
      elseif (isset($_SESSION['userObject']) && $_SESSION['userObject']->usergroup != User::USERGROUP_SUPERADMIN) { //mag eigen organisatie niet zien
        $filtquery .= " AND organisation.id!=" . $_SESSION['userObject']->organisation->id . " ";
        if ($_SESSION['userObject']->usergroup != User::USERGROUP_SUPERADMIN && $_SESSION['userObject']->usergroup != User::USERGROUP_ADMIN) {
          $filtquery .= "AND (organisation.type!='" . Organisation::TYPE_OTHER . "' OR organisation.type IS NULL) ";
        }
      }

      $filtquery .= Organisation::getFilterQueryExtra();

      return $filtquery;
    }

    /**
     * Used for building project specific queryfilter
     * @return string
     */
    public static function getFilterQueryExtra(): string {
      return '';
    }

    public static function getOrgantype(): array {
      return self::ORGANISATION_TYPES;
    }

    /**
     * Ophalen met array van mogelijke organisation.types die een bepaalde usergroup mag inzien/aanmaken. Ik krijg dus array() met key=organisation.type, value=organisation.type naam.
     * Dit word bijvoorbeeld gebruikt voor de select box van organisatie types bij het bewerken van een organisatie.
     * @param $usergroup
     * @return array
     */
    public static function getTypes($usergroup): array {
      if ($usergroup == User::USERGROUP_SUPERADMIN) {
        return Organisation::getOrgantype();
      }
      $organtypes = Config::get('organisation_types_usg');
      if (isset($organtypes[$usergroup])) {
        $ar = [];
        $kv_arr = Organisation::getOrgantype();
        foreach ($organtypes[$usergroup] as $organtype) {
          $ar[$organtype] = $kv_arr[$organtype];
        }
        return $ar;
      }
      return [];
    }

    /**
     * Get description of organisation type
     * @param $type
     * @return string
     */
    public static function getTypeDesc($type): string {
      if ($type == null || $type == '') return "";
      if (Trans::hasValue("ORGANTYPE_" . $type)) {
        return __("ORGANTYPE_" . $type);
      }
      $kv_arr = Organisation::getOrgantype();
      if (isset($kv_arr[$type])) {
        return __($kv_arr[$type]);
      }
      return '';
    }

    /**
     * @return mixed
     */
    public function getId() {
      return $this->id;
    }

    /**
     * Sets external_id {xxxxx-xxxx-xxx} without { AND }
     * @param string $external_id (required)
     */
    public function setExternalId($external_id): void {
      $external_id = str_replace(["{", "}"], "", $external_id);
      $this->external_id = $external_id;
    }

    public function getExternalId() {
      return $this->external_id;
    }

    /**
     * @return mixed
     */
    public function getExternalCode() {
      return $this->external_code;
    }

    /**
     * @param mixed $external_code
     */
    public function setExternalCode($external_code): void {
      $this->external_code = $external_code;
    }

    /**
     * @param string $nl : break
     * @param bool $showcountry : toon land
     * @return mixed
     */
    public function getAddress(string $nl = '<br/>', bool $showcountry = true): string {
      //for display only, so escaping single quotes to html codes
      $string = "";
      if ($this->address != null) {
        if ($this->address . $this->number . $this->extension != "") {
          $string .= trim($this->address . " " . $this->number . " " . $this->extension);
        }
        if ($this->zip . $this->city != "") {
          $string .= $nl;
          $string .= $this->zip . " " . $this->city;
        }
        if(Config::isTrue('ORGAN_USE_PROVINCE') && $this->province_id != "") {
          $string .= $nl;
          $string .= Province::getNameById($this->province_id);
        }
        if ($showcountry && $this->country != "") {
          $string .= $nl;
          $string .= __($this->getCountry());
        }

      }
      return displayAsHtml($string);
    }

    public function getEmail() {
      return $this->email;
    }

    /**
     * Get all country names sorted by name
     * @return array
     */
    public static function getCountries(): array {
      return Country::getCountriesSortedAr();
    }

    /**
     * Get all countries for delivery
     * @return array
     */
    public static function getDeliveryCountries(): array {
      if (Config::isdefined('deliverycountries')) {
        return Config::get('deliverycountries');
      }
      return self::getCountries();
    }

    /**
     * Get payment term of current organisation
     * @return int
     */
    public function getPaymentterm(): int {
      if ($this->paymentterm != "" && is_numeric($this->paymentterm)) {
        return (int)$this->paymentterm;
      }
      return Organisation::getDefaultPaymentterm();
    }

    /**
     * Get default paymentterm
     * @return int
     */
    public static function getDefaultPaymentterm(): int {
      if (Config::isdefined("ORGAN_PAYTMENTTERM_DEFAULT")) {
        return Config::get("ORGAN_PAYTMENTTERM_DEFAULT");
      }
      return 21;
    }


    /**
     * Get country name of current organisation
     * @return string
     */
    public function getCountry(): string {
      $countries = Organisation::getCountries();
      return isset($countries[$this->country]) ? __($countries[$this->country]) : "";
    }

    /**
     * Get country name of invoice country
     * @return string
     */
    public function getInvoiceCountry(): string {
      $countries = Organisation::getCountries();
      return $countries[$this->invoice_country];
    }

    /**
     * Get iban formatted
     * Indien $old_backaccount = true, wordt het "oude" rekeningnummer gereturned. vb: ****************** => ********* ( de eerste nul(len) worden afgehouden)
     * @param bool $old_bankaccount
     * @param bool $for_clieop
     * @return string iban
     */
    public function getIban(bool $old_bankaccount = false, bool $for_clieop = false): string {
      $iban = "";
      if ($old_bankaccount) {
        if (ValidationHelper::isIban($this->iban)) {
          $iban = ltrim(substr($this->iban, 8), '0');
        }
        else {
          $iban = str_replace(['.', ' '], '', $this->iban);
        }
      }
      else {
        $iban = str_replace(['.', ' '], '', $this->iban);
      }

      if ($for_clieop) {
        $iban = preg_replace("/[^0-9]+/", "", $iban);
      }
      return $iban;
    }

    public function getDetails(string $nl = '<br/>', bool $incl_contact = false): string {
      //for display only, so escaping single quotes to html codes
      $string = '';
      if (!empty($this->name)) {
        if ($nl == '<br/>') {
          $string .= '<b>' . $this->name . '</b>' . $nl;
        }
        else {
          $string .= $this->name . $nl;
        }
      }
      $string = displayAsHtml($string) . $this->getAddress($nl) . $nl;
      if ($incl_contact) {
        if (!empty($this->phone) && $this->phone!=".") {
          $string .= "T: ";
          if ($nl == '<br/>') {
            $string .= '<a href="tel:' . $this->phone . '">' . $this->phone . '</a>' . $nl;
          }
          else {
            $string .= $this->phone . $nl;
          }
        }
        //if($this->cellphone!="") $string .= $nl."M: " . $this->cellphone;
        if (!empty($this->email)) {
          $string .= "E-mail algemeen: ";
          if ($nl == '<br/>') {
            $string .= '<a href="mailto:' . $this->email . '">' . $this->email . '</a>' . $nl;
          }
          else {
            $string .= $this->email . $nl;
          }
        }
        if (!empty($this->website)) {
          $string .= "W: ";
          if ($nl == '<br/>') {
            $string .= '<a href="' . $this->website . '">' . $this->website . '</a>' . $nl;
          }
          else {
            $string .= $this->website . $nl;
          }
        }
      }

      return $string;
    }

    /**
     * Get bedrijfsnaam by id
     * @param $id
     * @return string
     */
    public static function getBedrijfsnaamById($id): string {
      if (empty($id)) return "";
      $organ = Organisation::find_by_id($id);
      if ($organ || empty($organ->name)) return "";
      return $organ->name;
    }

    public function getBedrijfsnaam(): string|null {
      return $this->name;
    }

    /**
     * @return mixed
     */
    public function getFacebookUrl() {
      return $this->getSocialUrl("facebook");
    }

    /**
     * @return mixed
     */
    public function getLinkedinUrl() {
      return $this->getSocialUrl("linkedin");
    }

    /**
     * Get social urls from organisation_profile
     * @param $type
     * @return bool|string
     */
    public function getSocialUrl($type) {
      $val = OrganisationProfile::getValueByOrganAndCode($this->id, $type);
      if ($val) {
        return $val;
      }
      return '';
    }


    /**
     * @param string $format (optional) default 'd-m-Y H:i:s'
     *
     * @return string
     */
    public function getInsertTS($format = 'd-m-Y H:i:s', $strftime = false) {
      if ($this->insertTS != "") {
        if (!$strftime) {
          return date($format, strtotime($this->insertTS));
        }
        else {
          return DateTimeHelper::strftime($format, strtotime($this->insertTS));
        }
      }

      return '';
    }

    /**
     * Get updateTS
     * @param string $format
     * @return string
     */
    public function getUpdateTS(string $format = 'd-m-Y H:i:s'): string {
      return DateTimeHelper::formatDbDate($this->updateTS, $format);
    }

    public function save(&$errors = []) {
      if ($this->from_db == false || $this->insertTS == "0000-00-00 00:00:00") {
        $this->insertTS = date('Y-m-d H:i:s');
        if (isset($_SESSION['userId'])) {
          $this->insertUser = $_SESSION['userId'];
        }
      }
      $this->updateTS = date('Y-m-d H:i:s');
      if (isset($_SESSION['userId'])) {
        $this->updateUser = $_SESSION['userId'];
      }
      $result = parent::save($errors);
      if (isset($_SESSION['userObject']) && $_SESSION['userObject']->organisation_id != $this->id) {
        $_SESSION['mayEdit'][$_SESSION['userObject']->id][$this->id] = true; //opslaan en zorgen dat deze user deze organisatie mag bewerken
      }
      return $result;
    }

    public function invoiceOrganisationTo() {
      if ($this->type == Organisation::TYPE_PARTICULIER) {
        //altijd factuur van uchair
        return ADMIN_DEFAULT_ID;
      }
      return $this->owner_organisation_id;
    }

    public function invoiceUserTo() {
      if ($this->type == Organisation::TYPE_PARTICULIER) {
        //altijd factuur van uchair
        return ADMIN_DEFAULT_ID;
      }
      return $this->owner_user_id;
    }

    /**
     * Get user which receives the invoices
     * @return User|false
     */
    public function getInvoiceUser(): User|false {
      $invoiceuser = User::find_by(['organisation_id' => $this->id, 'receives_invoice' => true], 'LIMIT 1');
      if (!$invoiceuser) {
        return User::find_by(['organisation_id' => $this->id, 'active' => true], 'LIMIT 1');
      }
      return $invoiceuser;
    }

    public function setInactive() {
      $this->active = false;
    }

    /**
     * Get organisation and organaddres by id
     * @param $organaddressid
     * @return Organisation|null
     * @throws GsdException
     */
    public static function getOrganAndOrganaddressByOrganAddressId($organaddressid): Organisation|null {
      $query = "SELECT * FROM organisation ";
      $query .= "JOIN organisation_address ON organisation.id = organisation_address.organisation_id ";
      $query .= "WHERE organisation_address.id = " . escapeForDB($organaddressid);

      $result = DBConn::db_link()->query($query);
      $organ = null;
      while ($row = $result->fetch_row()) {

        $column_counter = 0;
        $organ = new Organisation()->hydrateNext($row, $column_counter);
        $organ->organisation_address = new OrganisationAddress()->hydrateNext($row, $column_counter);;

        return $organ;
      }
      return $organ;
    }

    /**
     * Get al users with organisation by usergroup
     * @param string $usergroup
     * @return array
     */
    public static function getAllByUsergroup($usergroup) {
      $query = "SELECT * FROM user ";
      $query .= "JOIN organisation on user.organisation_id = organisation.id ";
      $query .= "WHERE  user.usergroup='" . escapeForDB($usergroup) . "' ";
      $query .= "ORDER BY organisation.name, user.lastname, user.firstname ";

      $users = [];
      $result = DBConn::db_link()->query($query);
      while ($row = $result->fetch_row()) {
        $column_counter = 0;
        $user = new User()->hydrateNext($row, $column_counter);
        $user->organisation = new Organisation()->hydrateNext($row, $column_counter);
        $users[$user->id] = $user;
      }
      return $users;
    }

    /**
     * Get lat and lng of organisation, as string with format "lat, lng"
     * @return string
     */
    public function getLatLng(): string {
      if ($this->lat != "" && $this->lng != "") {
        return $this->lat . ', ' . $this->lng;
      }
      //niet gevonden, probeer op te halen.
      if ($this->setLatLng()) {
        return $this->getLatLng();
      }
      return '';
    }

    /**
     * Set lat and lng of organisation by using Google Maps API
     * @return bool true if lat and lng are set, false if not
     * @throws GsdDbException
     * @throws GsdException
     */
    public function setLatLng(): bool {
      $addressNumber = $this->address . " " . $this->number;
      $latlng = LocationHelper::retrieveLatLngGoogle($this->zip, $addressNumber, $this->city, $this->country);
      if ($latlng) {
        $this->lat = $latlng['latitude'];
        $this->lng = $latlng['longitude'];
        $this->save();
        return true;
      }
      return false;
    }

    /**
     * Get new invoice number for this organisation
     * @param bool $save : save new invoice number to database
     * @return string
     */
    public function getNewInvoicenumber(bool $save = true): string {
      $nr = $this->last_invoice_nr + 1;
      if ($save) {
        $this->last_invoice_nr = $nr;
        $this->save();
        if (isset($_SESSION['userObject']) && $_SESSION['userObject']->id == $this->id) {
          //refresh object
          $_SESSION['userObject'] = User::getUserWithOrganById($_SESSION['userObject']->id);
        }
      }
      return 'FA_' . date('Y') . sprintf("%05d", $nr);
    }

    /**
     * Get all organisation where this organisation is owner of.
     * @param Organisation $owner
     * @param null $filter
     * @return Organisation[]
     * @throws GsdException
     */
    public static function getOrganisationsByOwnerId(Organisation $owner, $filter = null): array {
      $query = 'SELECT * FROM organisation ';
      $query .= 'WHERE organisation.owner_organisation_id = ' . escapeForDB($owner->id) . ' ';
      $query .= 'AND organisation.id != ' . escapeForDB($owner->id);
      if ($filter != null) {
        $query .= $filter;
      }

      $result = DBConn::db_link()->query($query);
      $organs = [];
      while ($row = $result->fetch_row()) {
        $organ = new Organisation()->hydrateNext($row);
        $organs[$organ->id] = $organ;
      }
      return $organs;
    }

    /**
     * Gets first user with organisation by organisation_id
     * @param $organid
     * @return User
     */
    public static function getOrganAndUserById($organid): User {
      $query = "SELECT * FROM organisation ";
      $query .= "JOIN user on user.organisation_id=organisation.id ";
      $query .= "WHERE organisation.id = " . escapeForDB($organid);
      $query .= " LIMIT 1 ";

      $result = DBConn::db_link()->query($query);
      $luser = new User();
      while ($row = $result->fetch_row()) {

        $column_counter = 0;
        $organisation = new Organisation()->hydrateNext($row, $column_counter);
        $luser->hydrateNext($row, $column_counter);
        $luser->organisation = $organisation;

      }
      return $luser;
    }

    /**
     * Get all organisations with users, optionally filtered by $filter
     * @param string|null $filter
     * @return Organisation[]
     * @throws GsdException
     */
    public static function getOrgansAndUsers(?string $filter): array {
      $query = "SELECT * FROM organisation ";
      $query .= "JOIN user on user.organisation_id=organisation.id ";
      $query .= "WHERE 1 ";
      $query .= $filter;

      $result = DBConn::db_link()->query($query);
      $organisations = [];
      $luser = new User();
      while ($row = $result->fetch_row()) {

        $column_counter = 0;
        $organisation = new Organisation()->hydrateNext($row, $column_counter);
        if (isset($organisations[$organisation->id])) {
          $organisation = $organisations[$organisation->id];
        }
        else {
          $organisation->users = [];
        }
        $luser->hydrateNext($row, $column_counter);
        $organisation->users[] = $luser;
        $organisations[$organisation->id] = $organisation;
      }
      return $organisations;
    }

    /**
     * Get organisation and all users by organisation id
     * @param $organid
     * @return Organisation|false
     * @throws GsdException
     */
    public static function getOrganAndAllUsers($organid): Organisation|false {
      $query = "SELECT * FROM organisation ";
      $query .= "LEFT JOIN user on user.organisation_id=organisation.id ";
      $query .= "WHERE organisation.id = " . escapeForDB($organid);

      $result = DBConn::db_link()->query($query);
      $organ = false;
      while ($row = $result->fetch_row()) {
        if (!$organ) {
          $organ = new Organisation();
          $organ->from_db = true;
          $organ->hydrate($row);
        }

        $luser = new User();
        $luser->hydrate($row, count(Organisation::columns));
        $luser->from_db = true;
        $organ->users[] = $luser;
      }
      return $organ;
    }

    /**
     * Get first added user in organisation
     * @return User
     */
    public function getFirstUser(): User|false {
      $l_user = User::find_by(['organisation_id' => $this->id, "void" => 0], 'ORDER BY id ASC');
      if (!$l_user) {
        $l_user = User::find_by(['organisation_id' => $this->id], 'ORDER BY id ASC');
      }
      return $l_user;
    }

    /**
     * Get domain name of organisation
     * @return string
     */
    public function getDomainName(): string {
      $site = Site::find_by(['organisation_id' => $this->id]);
      if ($site) {
        $sitehost = SiteHost::getPrimary($site->id);
        if ($sitehost) {
          return $sitehost->host;
        }
      }
      return '';
    }

    public function setOptions($options): void {
      $this->options = serialize($options);
    }

    public function getOptions() {
      return unserialize($this->options ?? '');
    }

    /**
     * @param string $optionname (required)
     * @return mixed
     */
    public function getOption($optionname) {
      $options = $this->getOptions();
      if ($options && $optionname != "" && isset($options[$optionname])) {
        return $options[$optionname];
      }
      return '';
    }

    /**
     * @param bool $returnDashIfEmpty : return - if is empty
     * @return string
     */
    public function getCustNr(bool $returnDashIfEmpty = true): string {
      if (Config::isTrue("ORGAN_CUST_NR_ENABLED")) {
        if ($returnDashIfEmpty && $this->cust_nr == '') {
          return '-';
        }
        return (string)$this->cust_nr;
      }
      return (string)$this->id;
    }

    /**
     * @param Organisation $organ (required)
     * @return bool
     */
    public static function mayDelete(Organisation $organ): bool {
      $children = User::getChildren($organ);
      if (Orders::tableExists()) {
        $orders = Orders::find_by(["organisation_id" => $organ->id]);
        if ($orders || count($children) > 0) {
          return false;
        }
      }
      if (Invoice::tableExists()) {
        $invoices = Invoice::find_by(["organisation_id" => $organ->id]);
        if ($invoices || count($children) > 0) {
          return false;
        }
      }
      return true;
    }

    /**
     * Get organisation profile
     * @return OrganisationProfile[]
     */
    public function getProfile(): array {
      if (!isset($this->profile)) {
        $this->profile = OrganisationProfile::getOrganisationProfileByOrganId($this->id);
      }
      return $this->profile;
    }

    /**
     * Has PDF template
     * @return bool
     */
    public function hasTemplate(): bool {
      return !empty($this->template) && file_exists($this->getTemplatePath());
    }

    /**
     * Get PDF template. Can be overriden on project level.
     * @return string
     */
    public function getTemplate(): string {
      return $this->template;
    }


    /**
     * Get PDF template path
     * @return string
     */
    public function getTemplatePath(): string {
      return DIR_UPLOADS . "templates/" . $this->getTemplate();
    }

    /**
     * Get PDF template URL
     * @return string
     */
    public function getTemplateUrl(): string {
      return URL_UPLOADS . "templates/" . $this->getTemplate();
    }

    /**
     * @inheritdoc
     * @return bool|mysqli_result
     */
    public function destroy($force = false) {

      if ($this->hasTemplate()) {
        unlink($this->getTemplatePath());
      }
      if ($this->logo_orgin != "" && file_exists(DIR_UPLOADS . 'logo/' . $this->logo_orgin)) {
        unlink(DIR_UPLOADS . 'logo/' . $this->logo_orgin);
      }
      if ($this->logo_prev != "" && file_exists(DIR_UPLOADS . 'logo/' . $this->logo_prev)) {
        unlink(DIR_UPLOADS . 'logo/' . $this->logo_prev);
      }
      if ($this->logo_thumb != "" && file_exists(DIR_UPLOADS . 'logo/' . $this->logo_thumb)) {
        unlink(DIR_UPLOADS . 'logo/' . $this->logo_thumb);
      }

      if (!$force && Config::isTrue("USER_VOID_ON_DESTROY")) {
        $this->void = 1;
        return $this->save();
      }
      else {
        return parent::destroy();
      }
    }

    /**
     * Validates organsation form
     * @return array
     */
    public function validateOrganisation(): array {
      $errors = [];
      if ($this->isOblidged("type") && $this->type == "") {
        $errors['type'] = __("Bedrijfstype");
      }
      if ($this->type != Organisation::TYPE_PARTICULIER && $this->type != Organisation::TYPE_JONGERE && $this->type != Organisation::TYPE_STARTER && (!Config::isdefined('ORGANISATION_NAME_OBLIDGED') || Config::isTrue("ORGANISATION_NAME_OBLIDGED")) && $this->name == "") {
        $errors['name'] = __("Bedrijfsnaam");
      }

      if ($this->isOblidged('address') && $this->address == "") {
        $errors['visitaddress'] = __("Bezoekadres") . ": ";
        $errors['address'] = __("Straat");
      }
      if ($this->isOblidged('number') && $this->number == "") {
        $errors['visitaddress'] = __("Bezoekadres") . ": ";
        $errors['number'] = __("Nummer");
      }
      if ($this->isOblidged('zip') && ($this->zip == "" || strlen($this->zip) > 12)) {
        $errors['visitaddress'] = __("Bezoekadres") . ": ";
        $errors['zip'] = __("Postcode");
      }
      if ($this->isOblidged('city') && $this->city == "") {
        $errors['visitaddress'] = __("Bezoekadres") . ": ";
        $errors['city'] = __("Plaats");
      }
      if ($this->country == "") {
        $errors['visitaddress'] = __("Bezoekadres") . ": ";
        $errors['country'] = __("Land");
      }

      if (Config::isTrue("ORGAN_EDIT_SHOW_MAP")) {
        if (empty($this->lat) || empty($this->lng) || !is_numeric($this->lat) || !is_numeric($this->lng)) {
          $errors['lat'] = true;
          $errors['lng'] = true;
          $errors['lat_lng'] = __("Geen geldige coördinaten bij het ingevoerde adres");
        }
      }
      if ($this->language == "") {
        $errors['language'] = __("Taal");
      }
      if ($this->invoice_equal == 0) {
        if ($this->invoice_address == "") {
          $errors['invoice_address'] = "Factuur straat";
        }
        if ($this->invoice_number == "") {
          $errors['invoice_number'] = "Factuur huisnummer";
        }
        if ($this->invoice_zip == "") {
          $errors['invoice_zip'] = "Factuur postcode";
        }
        if ($this->invoice_city == "") {
          $errors['invoice_city'] = "Factuur stad";
        }
        if ($this->invoice_country == "") {
          $errors['invoice_country'] = "Factuur land";
        }
      }

      if (!Config::isdefined("ORGANISATION_EMAIL_OBLIDGED") || Config::isTrue("ORGANISATION_EMAIL_OBLIDGED")) {
        if ($this->email == "") {
          $errors['organ_email'] = __('E-mailadres');
        }
        elseif (!ValidationHelper::isEmail($this->email)) {
          $errors['organ_email'] = __("Geen geldig email-adres.");
        }
      }
      elseif ($this->email != "" && !ValidationHelper::isEmail($this->email)) {
        $errors['organ_email'] = __('E-mailadres');
      }

      if ($this->website != "" && !ValidationHelper::isWebsite($this->website) && (!Config::isdefined("ORGANISATION_WEBSITE_VALIDATE") || Config::isTrue("ORGANISATION_WEBSITE_VALIDATE"))) {
        $errors['website'] = __("Geen geldige website");
      }
      if ($this->isOblidged("organ_phone") && $this->phone == "") {
        $errors['organ_phone'] = __("Telefoonnummer");
      }
      // vat number not required when organisation is in the Netherlands
      $invoice_country = ($this->invoice_equal == 1) ? $this->country : $this->invoice_country;
      if ($this->isOblidged("vat_number") && $this->vat_number == "" && $invoice_country != 'nl') {
        $errors['vat_number'] = __("BTW-nummer");
      }
      elseif ($this->vat_number != "" && !$this->validateVatNumber()) {
        $errors['vat_number'] = __("BTW-nummer ongeldig, of factuur-land/bezoek-land komt niet overeen met land BTW-nummer");
      }
      if ($this->isOblidged("coc_number") && $this->coc_number == "") {
        $errors['coc_number'] = __("KvK-nummer");
      }
      elseif ($this->coc_number != "" && !ValidationHelper::isKvk($this->coc_number, $this->country)) {
        $errors['coc_number'] = __("KvK-nummer ongeldig");
      }
      if (($this->isOblidged("email_invoice") || $this->email_invoice != "") && !ValidationHelper::isEmail($this->email_invoice) && $this->active == 1) {
        if (!Config::isdefined("ORGANISATION_INVOICE_TYPE_EDITABLE") || Config::isTrue("ORGANISATION_INVOICE_TYPE_EDITABLE")) {
          $options = $this->getOptions();
          if ($options['emailinvoice'] == 1) {
            $errors['email_invoice'] = __("Geen geldig factuur e-mailadres.");
          }
        }
        else {
          $errors['email_invoice'] = __("Geen geldig factuur e-mailadres.");
        }
      }

      if ($this->owner_organisation_id == "" || $this->owner_user_id == "") {
        if (!Config::isdefined("DONT_USE_OWNER_USER_ID") || Config::isFalse("DONT_USE_OWNER_USER_ID")) {
          $errors['owner_organisation_id'] = true;
          $errors['owner_user_id'] = __("Dit organisatie type moet altijd een eigenaar hebben.");
        }
      }
      else {
        $owner = Organisation::find_by_id($this->owner_organisation_id);
        if ($this->type == Organisation::TYPE_DISTRIBUTEUR && $owner->type != Organisation::TYPE_OTHER) {
          if (!Config::isdefined("DONT_USE_OWNER_USER_ID") || Config::isFalse("DONT_USE_OWNER_USER_ID")) {
            if ($this->type == Organisation::TYPE_DISTRIBUTEUR && $owner->type == Organisation::TYPE_DISTRIBUTEUR && $_SESSION["userObject"]->usergroup == User::USERGROUP_ADMIN) {
              //dit is een upgrade van AGENT naar DISTRIBUTEUR, en de ingelogde gebruiker is een admin. Dit is toegestaan., aangenaar aanpassen.
              $this->owner_organisation_id = $_SESSION["userObject"]->organisation_id;
              $this->owner_user_id = $_SESSION["userObject"]->id;
            }
            else {
              $errors['type'] = __("Dit organisatie type moet altijd een specifieke eigenaar hebben.");
            }
          }
        }
        elseif ($this->type == Organisation::TYPE_AGENT && $owner->type != Organisation::TYPE_DISTRIBUTEUR) {
          if (!Config::isdefined("DONT_USE_OWNER_USER_ID") || Config::isFalse("DONT_USE_OWNER_USER_ID")) {
            $errors['type'] = __("Dit organisatie type moet altijd een specifieke eigenaar hebben.");
          }
        }
        elseif ($this->type == Organisation::TYPE_KAPSALON && ($owner->type != Organisation::TYPE_DISTRIBUTEUR && $owner->type != Organisation::TYPE_AGENT)) {
          $errors['type'] = __("Dit organisatie type moet altijd een specifieke eigenaar hebben.");
        }
        elseif ($this->type == Organisation::TYPE_HOVENIER && $owner->type != Organisation::TYPE_OTHER) {
          $errors['type'] = __("Dit organisatie type moet altijd een specifieke eigenaar hebben.");
        }
      }

      if ($this->type == Organisation::TYPE_SHOP) {
        if ($this->iban == "") {
          $errors['iban'] = "IBAN";
        }
        if ($this->swift_bic == "") {
          $errors['swift_bic'] = "BIC";
        }
      }

      if ($this->iban != null && (strlen($this->iban) > 1) && !ValidationHelper::isIban($this->iban)) {
        $errors['iban'] = __("IBAN heeft een ongeldig formaat");
      }

      if ($this->isOblidged("nameof") && $this->nameof == "") {
        $errors['nameof'] = "Ten name van";
      }

      return $errors;
    }

    /**
     * Get organisation by cust_nr
     * @param $custnr
     * @return Organisation|false
     */
    public static function getByCustNr($custnr): Organisation|false {
      return Organisation::find_by(["cust_nr" => $custnr]);
    }

    /**
     * Bereken straal in km tussen 2 organisaties
     * @param Organisation $organ1
     * @param Organisation $organ2
     * @return float
     */
    public static function getDistance(Organisation $organ1, Organisation $organ2): float {
      return LocationHelper::calculateDistance($organ1->lat, $organ1->lng, $organ2->lat, $organ2->lng);
    }

    /**
     * Validate VAT number
     * @return bool
     */
    public function validateVatNumber(): bool {
      $valid = ValidationHelper::isVatnumber($this->vat_number);
      if ($valid) {
        //de eerste letters van het vatnummer moeten overeenkomen met het land.
        if ($this->invoice_equal == 0) {
          //ander factuuradres, en factuur land moet overeenkomen met de eerste 2 letters van de vat
          if (strtoupper($this->invoice_country) != substr($this->vat_number, 0, 2)) {
            $valid = false;
          }
        }
        elseif (strtoupper($this->country) != substr($this->vat_number, 0, 2)) {
          //land moet overeenkomen met de eerste 2 letters van de vat
          $valid = false;
        }
      }
      return $valid;
    }

    /**
     * Creates a new Organisation instance filled with data from DB, ensuring compatibility between the class model and database structure.
     * @param int $organisation_id
     * @return Organisation
     * @throws GsdException
     */
    public static function createOrganisationFromDB(int $organisation_id): Organisation {
      $query = "SELECT * FROM organisation WHERE id = " . $organisation_id;
      $result = DBConn::db_link()->query($query);
      $organ = $result->fetch_array();
      $organisation = new Organisation();
      $organisation->from_array($organ);
      return $organisation;
    }

  }

