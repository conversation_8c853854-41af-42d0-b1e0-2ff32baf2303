<?php

  use Gsd\Updater\UpdateChecker;

  /**
   * Helper function for templates
   */
  class TemplateHelper {

    /**
     * Get an html partial
     * @param string $name : partial name for instance _user.php
     * @param null|string $module : module name or null if it's in the current module
     * @param null|array $vars : array with variables
     * @param string $plugin : plugin
     * @return false|string
     */
    public static function getPartial(string $name, $module = null, $vars = null, $plugin = "") {
      ob_start();
      TemplateHelper::includePartial($name, $module, $vars, $plugin);
      $out = ob_get_contents();
      ob_end_clean();
      return $out;
    }

    /**
     * Include an html partial
     *
     * For example: TemplateHelper::includePartial("_review.php", "pingpongtafels", compact("pptorder"))
     *
     * @param string $l_name : partial name for instance _user.php
     * @param null|string $l_module : module name or null if it's in the current module
     * @param null|array $l_vars : array with variables
     * @param string $l_plugin : plugin
     */
    public static function includePartial(string $l_name, $l_module = null, $l_vars = null, $l_plugin = "") {
      if (!isset($l_vars['site'])) {
        $l_vars['site'] = Context::getSite(); //always give Site to the partial. this is handy for the template Url for instance
      }
      if ($l_vars != null) {
        extract($l_vars);
      }
      if ($l_module == null) {
        require($l_name);
      }
      elseif ($l_name != "") { //kan voorkomen als een component geen template bevat
        if (file_exists(DIR_PROJECT_FOLDER . 'modules/' . $l_module . '/templates/' . $l_name)) {
          include(DIR_PROJECT_FOLDER . 'modules/' . $l_module . '/templates/' . $l_name);
        }
        elseif (Config::isdefined("GSDFW_PROJECT_EXTENDS") && Config::get("GSDFW_PROJECT_EXTENDS") != "" && file_exists(DIR_ROOT . 'projects/' . strtolower(Config::get("GSDFW_PROJECT_EXTENDS")) . '/modules/' . $l_module . '/templates/' . $l_name)) {
          include(DIR_ROOT . 'projects/' . strtolower(Config::get("GSDFW_PROJECT_EXTENDS")) . '/modules/' . $l_module . '/templates/' . $l_name);
        }
        elseif ($l_plugin != "" && file_exists(DIR_PLUGIN_FOLDER . $l_plugin . '/modules/' . $l_module . '/templates/' . $l_name)) {
          include(DIR_PLUGIN_FOLDER . $l_plugin . '/modules/' . $l_module . '/templates/' . $l_name);
        }
        else {
          require(DIR_MODULES . $l_module . '/templates/' . $l_name);
        }
      }
    }


    /**
     * Include an html partial for de index of the backend , and send all action variables to partial.
     * @param string $l_name partial name for instance _user.php
     * @param gsdActions $l_action module name or null if it's in the current module
     * @param null|array $vars_in extra array with variables
     */
    public static function includeBackendIndexPartial(string $l_name, gsdActions $l_action, $l_vars = []) {
      $l_vars['current_action'] = Context::getAction(); //always action object to all index templates
      $l_vars['site'] = Context::getSite(); //always give Site to the partial. this is handy for the template Url for instance
      $l_vars = array_merge($l_vars, get_object_vars($l_action));
      extract($l_vars);

      if (file_exists(DIR_PROJECT_FOLDER . 'templates/' . Context::getSite()->template . '/pages/partials/' . $l_name)) {
        include(DIR_PROJECT_FOLDER . 'templates/' . Context::getSite()->template . '/pages/partials/' . $l_name);
        return;
      }

      if(PROJECT=="bodel") {
        //bodel valt  nog even terug naar oude backend. inlog scherm viel om.
        require(DIR_ROOT_GSDFW . 'projects/default/templates/backend/pages/partials/' . $l_name);
        return;
      }

      if(PROJECT=="vdlcontainer") {
        //vdlcontainer gebruikt backend en backend2 partials...even wachten tot manu heeft omgezet
        require(DIR_ROOT_GSDFW . 'projects/default/templates/' . Context::getSite()->template . '/pages/partials/' . $l_name);
        return;
      }

      if(Context::getSite()->template == "backend") {
        throw new GsdException("Er word een oude backend partial aangeroepen? Dit moet backend2 zijn. Template: " . Context::getSite()->template);
      }

      require(DIR_ROOT_GSDFW . 'projects/default/templates/backend2/pages/partials/' . $l_name);

    }

    /**
     * Include a component
     * @param string $name : name of component for instance tabs
     * @param string $lmodule : module name
     * @param null|array $vars : array with variables
     * @param string $plugin : plugin component
     * @return void
     * @throws Exception
     */
    public static function includeComponent(string $name, string $lmodule, $vars = null, $plugin = ""): void {
      //load component
      $classname = $lmodule . 'Components';
      if (file_exists(DIR_MODULES . $lmodule . '/actions/components.class.php')) {
        require_once(DIR_MODULES . $lmodule . '/actions/components.class.php');
      }
      if (Config::isdefined("GSDFW_PROJECT_EXTENDS") && Config::get("GSDFW_PROJECT_EXTENDS") != "" && file_exists(DIR_ROOT . 'projects/' . strtolower(Config::get("GSDFW_PROJECT_EXTENDS")) . '/modules/' . $lmodule . '/actions/components.class.php')) {
        require_once(DIR_ROOT . 'projects/' . strtolower(Config::get("GSDFW_PROJECT_EXTENDS")) . '/modules/' . $lmodule . '/actions/components.class.php');
        if (class_exists($lmodule . ucfirst(strtolower(Config::get("GSDFW_PROJECT_EXTENDS"))) . 'Components')) {
          $classname = $lmodule . ucfirst(strtolower(Config::get("GSDFW_PROJECT_EXTENDS"))) . 'Components';
        }
      }
      if ($plugin != "" && file_exists(DIR_PLUGIN_FOLDER . $plugin . '/modules/' . $lmodule . '/actions/components.class.php')) {
        require_once(DIR_PLUGIN_FOLDER . $plugin . '/modules/' . $lmodule . '/actions/components.class.php');
        if (class_exists($lmodule . 'Components')) {
          $classname = $lmodule . 'Components';
        }
      }
      if (file_exists(DIR_PROJECT_FOLDER . 'modules/' . $lmodule . '/actions/components.class.php')) {
        require_once(DIR_PROJECT_FOLDER . 'modules/' . $lmodule . '/actions/components.class.php');
        if (class_exists($lmodule . ucfirst(strtolower(PROJECT)) . 'Components')) {
          $classname = $lmodule . ucfirst(strtolower(PROJECT)) . 'Components';
        }
      }
      $component = new $classname;
      if (!method_exists($component, 'execute' . ucfirst($name))) {
        throw new Exception('Unknown component: ' . $name . ' in controller ' . $classname);
      }

      //toevoegen vars aan object
      if ($vars != null) {
        foreach ($vars as $varnam => $var) {
          $component->{$varnam} = $var;
        }
      }

      $component->componenttemplate = '_' . $name . '.php';

      call_user_func([$component, 'preExecute']);
      call_user_func([$component, 'execute' . ucfirst($name)]);

      if (isset($component->template)) {
        $component->componenttemplate = $component->template;
      }

      if ($vars == null) {
        $vars = get_object_vars($component);
      }
      else {
        $vars = array_merge($vars, get_object_vars($component)); //component variabelen toevoegen aan partial
      }
      TemplateHelper::includePartial($component->componenttemplate, $lmodule, $vars, $plugin);
    }

    /**
     * Get an html component
     * @param string $name : name of component for instance tabs
     * @param string $lmodule : module name
     * @param null|array $vars : array with variables
     * @param string $plugin : plugin component
     * @return string
     * @throws Exception
     */
    public static function getComponent(string $name, string $lmodule, $vars = null, $plugin = ""): string {
      ob_start();
      TemplateHelper::includeComponent($name, $lmodule, $vars, $plugin);
      $out = ob_get_contents();
      ob_end_clean();
      return $out;
    }

    /**
     * Include a cached component
     * @param string $name : name of component for instance tabs
     * @param string $lmodule : module name
     * @param null $vars : array with variables
     * @param int $cacheTime : time to cache in seconds
     * @param string $filename
     * @param string $plugin : plugin component
     * @return void
     * @throws Exception
     */
    public static function includeCachedComponent(string $name, string $lmodule, $vars = null, int $cacheTime = 3600, string $filename = "", $plugin = ""): void {
      if (empty($filename)) {
        $filename = $lmodule . "-" . $name . ".html";
      }

      if (CacheHelper::isValid($filename, $cacheTime)) {
        echo CacheHelper::getCacheFile($filename);
        return;
      }
      $content = TemplateHelper::getComponent($name, $lmodule, $vars, $plugin);
      CacheHelper::putCacheFile($filename, $content);
      echo $content;
    }

    /**
     * Write topbar staging/development message
     * @return void
     */
    public static function writeStagingMessage(): void {
      if (ENVIRONMENT == 'LOCAL') {
        echo '<a id="message_development" href="/nl/developer">'.ENVIRONMENT.' - ' . ucfirst(PROJECT) . '</a>';
      }
      elseif (ENVIRONMENT == 'STAGING') {
        echo '<div id="message_development">'.ENVIRONMENT.' - ' . ucfirst(PROJECT) . '</div>';
      }
    }

    /**
     * Include a JS file in the html, with a version number based on the file modified time
     *
     * @param string $file_path_no_extension relative filepath without the .css, eg: $site->getTemplateUrl() . 'js/main'
     * @param bool $has_minified if there is a minified version, it will render the .min file based on DEVELOPMENT status
     * @return string
     */
    public static function includeJavascript(string $file_path_no_extension, bool $has_minified = false): string {
      $html_code = '<script type="text/javascript" ';
      // add the filestamp to the filename, so the browser will always fetch the latest version
      $html_code .= 'src="' . self::getVersionedAsset($file_path_no_extension, '.js', $has_minified);
      $html_code .= '"></script>' . "\n";
      return $html_code;
    }

    /**
     * Include a CSS file in the html, with a version number based on the file modified time
     *
     * @param string $file_path_no_extension relative filepath without the .css, eg: $site->getTemplateUrl() . 'style/main'
     * @param bool $has_minified if there is a minified version, it will render the .min file based on DEVELOPMENT status
     * @return string
     */
    public static function includeStylesheet(string $file_path_no_extension, bool $has_minified = false): string {
      $html_code = '<link rel="stylesheet" type="text/css" ';
      // add the filestamp to the filename, so the browser will always fetch the latest version
      $html_code .= 'href="' . self::getVersionedAsset($file_path_no_extension, '.css', $has_minified);
      $html_code .= '"/>' . "\n";
      return $html_code;
    }

    /**
     * Get url path of file, add version to url based on the file modified time
     *
     * @param string $file_path_no_extension relative filepath without the .css, eg: $site->getTemplateUrl() . 'style/main'
     * @param string $extension extension of the file, with dot, eg .js or .css
     * @param bool $has_minified if there is a minified version, it will render the .min file based on ENVIRONMENT status. If NOT LOCAL, it will render the minified version
     * @param bool $force_timestamp if any issues occur with the asset version, overwrite with current unix timestamp
     * @return string
     */
    public static function getVersionedAsset(string $file_path_no_extension, string $extension, bool $has_minified = false, bool $force_timestamp = false): string {
      $url_file_path = $file_path_no_extension . (($has_minified && ENVIRONMENT != 'LOCAL') ? '.min' : '') . $extension;
      $file_path_with_version = $url_file_path;
      $file_path_with_version .= '?v=';

      $versioned_timestamp = get_asset_version(DIR_ROOT . $url_file_path);
      if ($force_timestamp) $versioned_timestamp = date('U');
      $file_path_with_version .= $versioned_timestamp;

      return $file_path_with_version;
    }

    /**
     * Get pagecontent from database or gsdeditor
     * @param Page $page
     * @return array|false|string|string[]|null
     */
    public static function getPageContent(Page $page) {
      if ($page->isBlockeditor()) {
        return TemplateHelper::getComponent("gsdeditor", "gsdeditor", compact("page"));
      }
      return process_text($page->content->content1);
    }

    /**
     * Get pagecontents from database or gsdeditor
     * @param Page $page
     * @return array|false|string|string[]|null
     */
    public static function getPageContents(Page $page): array|bool|string|null {
      if ($page->isBlockeditor()) {
        return TemplateHelper::getComponent("gsdeditor", "gsdeditor", compact("page"));
      }
      $content = process_text($page->content->content1);
      $content_2 = !empty($page->content->content2) ? process_text($page->content->content2) : null;

      if (!empty($content_2)) {
        $content .= $content_2;
      }

      return $content;
    }

    /**
     * Write topbar updater messages
     * @param bool $inline_styling option to add styling inline
     * @return void
     * @throws Exception
     */
    public static function writeUpdaterMessages(bool $inline_styling = false): void {

      if(!(isset($_SESSION['userObject']) && $_SESSION['userObject']->usergroup == User::USERGROUP_SUPERADMIN)) return;

      $updates = UpdateChecker::check();
      if (!ArrayHelper::hasData($updates)) return;

      echo '<div id="message_updater" style="' .
        ($inline_styling ? 'background-color: #f44336; text-align: center; color: white; font-weight: bold; padding: 5px;' : '') .
        '"><a href="/nl/developer?action=updater" style="color:white;">UPDATERS DRAAIEN</a><ul>';
      foreach ($updates as $updater => $versions) {
        $message = sprintf(
          "<u style='%s'>%sUpdater</u> van versie %s naar %s",
          ($inline_styling ? 'list-style: none; margin: 0; padding: 0; font-weight: normal' : ''),
          ucfirst($updater), $versions['current'], $versions['latest']);
        echo '<li>' . $message . '</li>';
      }
      echo '</ul></div>';
    }

  }