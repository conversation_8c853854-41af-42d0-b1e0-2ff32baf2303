<?php

  namespace domain\basket\service;

  use Product;

  class BasketService {
    public function addToBasket($productId, $quantity) {
      $basket = $_SESSION['basket'] ?? ['items' => []];
      $existingKey = array_search($productId, array_column($basket['items'], 'product_id'));

      if ($existingKey !== false) {
        $basket['items'][$existingKey]['quantity'] += $quantity;
      } else if ($product = Product::getProductAndContent($productId)) {
        $basket['items'][] = [
          'product_id' => $productId,
          'product' => $product,
          'quantity' => $quantity,
        ];
      }
      $_SESSION['basket'] = $basket;
    }

    public function removeFromBasket($productId) {
      $basket = $_SESSION['basket'] ?? ['items' => []];
      $existingKey = array_search($productId, array_column($basket['items'], 'product_id'));
      if ($existingKey !== false) {
        unset($basket['items'][$existingKey]);
        $basket['items'] = array_values($basket['items']);
      }
      $_SESSION['basket'] = $basket;
    }

    public function getBasket() {
      return $_SESSION['basket'] ?? ['items' => []];
    }
  }