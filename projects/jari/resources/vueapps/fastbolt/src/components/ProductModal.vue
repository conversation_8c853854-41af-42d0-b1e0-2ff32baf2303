<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  product: {
    type: Object,
    default: null
  },
  container: {
    type: Object,
    default: null
  },
  isOpen: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'add-to-basket']);

const dialog = ref(null);

watch(() => props.isOpen, (newValue) => {
  if (newValue && dialog.value) {
    dialog.value.showModal();
    document.body.style.overflow = 'hidden';
  } else if (dialog.value) {
    dialog.value.close();
    document.body.style.overflow = '';
  }
});

function closeDialog() {
  emit('close');
}

function handleDialogClick(event) {
  if (event.target === dialog.value) {
    closeDialog();
  }
}

function handleKeydown(event) {
  if (event.key === 'Escape') {
    closeDialog();
  }
}

function handleAddToBasket() {
  emit('add-to-basket', props.product);
  closeDialog();
}
</script>

<template>
  <dialog
      ref="dialog"
      class="product-dialog"
      @click="handleDialogClick"
      @keydown="handleKeydown"
  >
    <div class="dialog-content" v-if="container && product">
      <div class="dialog-header">
        <h2>{{ container.content.name }}</h2>
        <button
            type="button"
            class="close-button"
            @click="closeDialog"
            aria-label="Close dialog"
        >
          ×
        </button>
      </div>

      <div class="dialog-body">
        <div class="info-card">
          <div class="card-header">
            <h3>Productgroep Specificaties</h3>
          </div>
          <div class="spec-grid">
            <div
                v-for="option in container.options"
                :key="option.code"
                class="spec-row"
            >
              <span class="spec-label">{{ option.name }}</span>
              <span class="spec-value">{{ option.value }}</span>
            </div>
          </div>
        </div>

        <div class="info-card">
          <div class="card-header">
            <h3>Product Specificaties</h3>
          </div>

          <div class="spec-grid" v-if="product.code">
            <div class="spec-row">
              <span class="spec-label">Artikelnummer</span>
              <span class="spec-value">{{ product.code }}</span>
            </div>
          </div>

          <div class="spec-grid" v-if="product.options && product.options.length">
            <div
                v-for="option in product.options"
                :key="option.code"
                class="spec-row"
                :class="{ 'highlight': option.name === 'Niet voor' }"
            >
              <span class="spec-label">{{ option.name }}</span>
              <span class="spec-value" :class="{ 'text-warning': option.name === 'Niet voor' }">
                {{ option.value }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div class="dialog-footer">
        <button type="button" class="btn btn-primary" @click="handleAddToBasket">
          Toevoegen
        </button>
      </div>
    </div>
  </dialog>
</template>

<style scoped lang="scss">
.product-dialog {
  border: none;
  border-radius: 8px;
  padding: 0;
  max-width: 500px;
  width: 90vw;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  margin: auto;

  &::backdrop {
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
  }
}

.dialog-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background-color: var(--color-primary, #007bff);
  color: white;
  border-radius: 8px 8px 0 0;
  position: sticky;
  top: 0;
  z-index: 10;

  h2 {
    margin: 0;
    font-size: 1.25rem;
  }
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }
}

.dialog-body {
  padding: 1.5rem;
}

.info-card {
  border-radius: 10px;
  border: 1px solid #e9ecef;
  margin-bottom: 1.5rem;
  overflow: hidden;

  &:last-child {
    margin-bottom: 0;
  }
}

.card-header {
  padding: 1rem 1.5rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;

  h3 {
    margin: 0;
    font-size: 1rem;
    color: #495057;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.spec-row {
  display: flex;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }
}

.spec-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
  flex: 1;
}

.spec-value {
  font-size: 0.875rem;
  color: #1f2937;
  font-weight: 600;
  text-align: right;
  max-width: 60%;
  word-break: break-word;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e5e5;
  background-color: #f8f9fa;
  border-radius: 0 0 8px 8px;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;

  &.btn-secondary {
    background-color: #6c757d;
    color: white;

    &:hover {
      background-color: #5a6268;
    }
  }

  &.btn-primary {
    background-color: var(--color-primary, #007bff);
    color: white;

    &:hover {
      background-color: var(--color-primary-dark, #0056b3);
    }
  }
}

@media (max-width: 768px) {
  .product-dialog {
    width: 95vw;
    max-width: none;
  }

  .spec-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
  }

  .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .dialog-body, .dialog-footer {
    padding: 1rem 1.25rem;
  }

  .card-header, .spec-row {
    padding: 0.75rem 1.25rem;
  }
}
</style>