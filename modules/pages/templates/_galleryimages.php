<?php if (!empty($finished_cropping)):
  //indien er gecropt is dan sluiten popup
  ?>
  <script type="text/javascript">
    $(document).ready(function () {
      if (parent.gsdModalCropper !== undefined) {
        parent.gsdModalCropper.close();
      }
    });
  </script>
  <?php
  return;
endif;
?>
<link rel="stylesheet" type="text/css" href="/gsdfw/includes/fileuploader/client/fileuploader.css"/>
<script src="<?php echo URL_INCLUDES ?>fileuploader/client/fileuploader.js" type="text/javascript"></script>
<?php if ($ckeditor) : ?>
  <?php echo TemplateHelper::includeJavascript('/gsdfw/includes/ckeditor4/ckeditor'); ?>
  <?php echo TemplateHelper::includeJavascript('/gsdfw/includes/jsscripts/ckeditor4'); ?>
  <script type="text/javascript">
    $(document).ready(function () {

      ckeditorInit();
      CKEDITOR.config['contentsCss'] = CKEDITOR.config['contentsCss'].concat([<?php echo getCkeditorStylesheets() ?>]);
      ckeditorScaytNl();
      ckeditorSimple();

      CKEDITOR.config['height'] = 70;
      CKEDITOR.config.disableNativeSpellChecker = false;

      <?php if(Config::get('PAGE_CKEDITOR_FILEBROWSER', true)):
      $_SESSION['filebrowserPath'] = DIR_UPLOADS . 'sites/' . $currentsite->id . '/ckfiles';
      ?>
      CKEDITOR.config['filebrowserBrowseUrl'] = '//<?php echo Context::getSiteDomain() . URL_INCLUDES ?>ckeditor4/RichFilemanager/index.html';
      <?php endif; ?>

    });
  </script>
<?php endif; ?>
<script type="text/javascript">

  gsdModalCropper = new GsdModal("cropper-modal");
  gsdModalCropper.setWidth("950px")
  gsdModalCropper.setHeight("700px")
  gsdModalCropper.init();

  $(document).ready(function () {

    new SimpleLightbox(".popupimage", {
      fileExt: false,
    });


    $(".cropper_popup").on("click", function (e) {
      e.preventDefault();
      gsdModalCropper.openIframe($(this).attr("href"), "Bijsnijden");
    });

    $(document).on('gsdModalClose', function (e, modalId) {
      if (modalId === "cropper-modal") {
        window.location.reload();
      }
    })

    gsdRowSorter("<?php echo reconstructQueryAdd(['pageId', 'parentid', 'id']) ?>gallid=<?php echo $gallery->id ?>&action=galimagemove&");

    createUploader();

    $(document).on("click", ".deletefile", function (event) {
      $(this).parent().hide().find("input").val("");
      event.preventDefault();
    });

    <?php if(Config::isdefined('PAGE_IMAGESGALLERY_URL') && (Config::get('PAGE_IMAGESGALLERY_URL') === 'ALL' || in_array($page->id, Config::get('PAGE_IMAGESGALLERY_URL')))): ?>
    $(".openlink").on("click", function (e) {
      e.preventDefault();
      var link = $(this).parent().find(".openlinkhref").val();
      if (link.substring(0, 1) == "/") { //add host if starts with /
        link = '<?php
          $firstsitehost = SiteHost::getPrimary($currentsite->id);
          if (!$firstsitehost) {
            $firstsitehost = SiteHost::find_by_id($currentsite->id);
          }
          echo $firstsitehost->getDomainSmart(true);
          ?>' + link;
      }
      if (link != "") {
        window.open(link, '_blank');
      }
    });
    <?php endif ?>


  });

  function createUploader() {
    var uploader = new qq.FileUploader({
      element: $("#fileuploader")[0],
      action: '<?php echo reconstructQuery(['action']) ?>action=upload',
      //debug: true,
      //extraDropzones: [qq.getByClass(document, 'qq-upload-extra-drop-area')[0]]
      allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
      uploadButtonText: 'Upload bestand(en)',
      cancelButtonText: 'Annuleren',
      failUploadText: 'Upload error',
      messages: {
        //serverError: "Some files were not uploaded, please contact support and/or try again.",
        typeError: "{file} heeft een ongeldige extensie. Alleen {extensions} zijn toegestaan.",
        sizeError: "{file} is te groot, maximum bestandsgrootte is {sizeLimit}.",
        emptyError: "{file} is leeg, selecteer uw bestanden opnieuw.",
        limitError: "U kan maximaal {limit} bestanden uploaden."
      },
      onComplete: function (id, fileName, responseJSON) {
        $('.qq-upload-success').each(function () {
          if ($(this).find(".deletefile").length == 0) {
            $(this).append('<a href="" class="deletefile"><?php echo IconHelper::getCrossSimpleRed() ?></a>')
              .append('<input type="hidden" value="' + fileName + '" name="send[files][]"/>');
          }
        });
        $("#saveimage").show();
        $("#saveimage_go").trigger("click");
      }
    });

  }
</script>

<?php if (Config::isdefined("MAX_NUMBER_OF_IMAGES")): ?>
  <?php if ($number_of_images < $allowed_number_of_images):
    echo '<b>U heeft momenteel ' . $number_of_images . ' van de ' . $allowed_number_of_images . ' beelden geüpload</b><br/><br/>';
    ?>
    <div id="fileuploader"></div><br/>
    <div id="saveimage" style="display:none;margin-bottom: 15px;">
      <input type="submit" id="saveimage_go" class="gsd-btn gsd-btn-primary" value="Geuploade bestanden plaatsen" name="handleuploads"/>
    </div>
  <?php
  else :
    echo '<div class="box" style="color: blue;">U heeft uw maximaal aantal te uploaden beelden van ' . $allowed_number_of_images . ' bereikt.<br/><br/>Verwijder één of neem contact op met GSD</div>';
    echo '<br/>';
  endif;
  ?>
<?php else: ?>
  <div id="fileuploader"></div><br/>
  <div id="saveimage" style="display:none;margin-bottom: 15px;">
    <input type="submit" id="saveimage_go" class="gsd-btn gsd-btn-primary" value="Geuploade bestanden plaatsen" name="handleuploads"/>
  </div>
<?php endif; ?>


<input type="button" id="sortablecontrol" name="" value="Sorteren aanzetten"
       class="gsd-btn gsd-btn-secondary"/> <?php echo showHelpButton("Door op 'sorteren aanzetten' te klikken wordt het mogelijk om de rijen van de tabel te verslepen, en zo de volgorde van de beelden aan te passen.", 'Sorteren'); ?>
<br/><br/>
<div id="message"></div>

<?php if (count($gallery->images) == 0) : ?>
  Er zijn nog geen beelden geupload in deze galerij.
<?php else : ?>
  <table class="default_table" id="sortable">
    <tr class="dataTableHeadingRow nodrop nodrag">
      <td class="sort-td"></td>
      <td>Beeld</td>
      <td>
        Alt tekst en
        omschrijving <?php echo showHelpButton('Vul hier een alternatieve tekst in voor uw beeld, dit is meteen goed voor uw vindbaarheid.', 'Alternatieve tekst voor beeld'); ?>
        <?php if (count($currentsitelanguages) > 1) { ?>
          <div style="display: inline-block;vertical-align: top;">
          <script type="text/javascript">
            $(document).ready(function () {
              var lang = <?php echo json_encode(LanguageHelper::getLanguagesNL()) ?>;

              $('a.languageselect').on("click", function (item) {
                $('a.languageselect img').css('border', '1px solid white');
                $('div.languagebox').hide();
                $(this).find("img").css('border', '1px solid blue');
                $('.content_' + $(this).attr("id")).show();
                $('#currentlanguage').text(lang[$(this).attr("id")]);
              });
              $('#<?php echo $_SESSION['worklanguage'] ?>').trigger("click");
            });
          </script>
          <?php
          foreach ($currentsitelanguages as $llang) {
            echo '<a href="javascript:void(0);" class="languageselect" id="' . $llang . '"><img alt="' . $llang . '" src="/gsdfw/images/flags/' . $llang . '.png"  style="width: 22px; height: 15px; margin-right: 5px; border: 1px solid white;"></a>';
          }
          echo ' Huidige taal: <span id="currentlanguage"></span>';
          ?></div><?php
        } ?>
      </td>
      <?php if ($use_cropping_tool) : ?>
        <td>Crop</td>
      <?php endif; ?>
      <td class="action">Acties</td>
    </tr>
    <?php foreach ($gallery->images as $image) : ?>
      <tr class="dataTableRow trhover" id="trimg_<?php echo $image->id ?>">
        <td class="sort-td"><span class="fa fa-bars"></span></td>
        <td>
          <a href="<?php echo $image->getUrlOrig($currentsite) ?>?time=<?php echo time(); ?>" class="popupimage qtipa" title="klik voor preview....">
            <img src="<?php echo $image->getUrlThumb($currentsite) ?>?time=<?php echo time(); ?>" alt="klik voor preview...." style="border-color:#681821; max-height: 150px; "/>
          </a>
        </td>
        <td style="vertical-align: top">
          <?php foreach ($currentsitelanguages as $langkey => $llang): ?>
            <div class="content_<?php echo $llang ?> languagebox" <?php if (count($currentsitelanguages) == 1): ?>style="display: block;"<?php endif; ?>>
              <input type="text" value="<?php echo escapeForInput($image->contents[$llang]->alt) ?>" name="alt[<?php echo $image->id ?>][<?php echo $llang ?>]"
                     placeholder="Alternatieve tekst..."/><br/>
              <?php if (!Config::isdefined("PAGE_SHOW_IMAGE_DESCRIPTION") || Config::get("PAGE_SHOW_IMAGE_DESCRIPTION")): ?>
                <textarea name="content[<?php echo $image->id ?>][<?php echo $llang ?>]" style="height: 70px; margin-top: 5px;width: 100%;" class="ckeditor"
                          placeholder="Extra omschrijving..."><?php echo $ckeditor ? $image->contents[$llang]->content : nl2br($image->contents[$llang]->content ?? ''); ?></textarea>
              <?php endif; ?>
              <?php if (Config::isdefined('PAGE_IMAGESGALLERY_URL') && (Config::get('PAGE_IMAGESGALLERY_URL') === 'ALL' || in_array($page->id, Config::get('PAGE_IMAGESGALLERY_URL')))): ?>
                <br/>
                <input type="text" class="openlinkhref" value="<?php echo escapeForInput($image->contents[$llang]->href) ?>"
                       name="href[<?php echo $image->id ?>][<?php echo $llang ?>]" placeholder="Link..." id="url_<?php echo $llang ?>"/>
                <a href="#" class="openlink" title="Open url op uw website"><span class="fa fa-link"></span></a>
                <br/>
              <?php endif; ?>
            </div>
          <?php endforeach; ?>
        </td>
        <?php if ($use_cropping_tool) : ?>
          <td>
            <a href="<?php echo reconstructQueryAdd(['pageId', 'parent_id', 'id', 'gallid']) . 'action=cropimagetool&type=galleryimage&imageid=' . $image->id; ?>" class="qtipa cropper_popup">
              <img src="/gsdfw/images/image_crop.png" alt="Bijsnijden"/>
            </a>
          </td>
        <?php endif; ?>
        <td>
          <?php echo BtnHelper::getRemove(reconstructQuery(['action']) . "action=galleryimagedelete&delid=" . $image->id) ?>
        </td>
      </tr>
    <?php endforeach; ?>
  </table>
<?php endif; ?>
