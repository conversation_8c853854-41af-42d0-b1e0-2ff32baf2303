<?php if (!empty($finished_cropping)):
  //indien er gecropt is dan sluiten popup
  ?>
  <script type="text/javascript">
    $(document).ready(function () {
      if (parent.gsdModalCropper !== undefined) {
        parent.gsdModalCropper.close();
      }
    });
  </script>
  <?php
  return;
endif;
?>

<section class="title-bar">
  <h1>
    <?php echo Navigation::getItem(Navigation::getActivePageId())->getName() ?>
  </h1>

  <?php TemplateHelper::includePartial('_tabs.php', 'siteadmin', ['site' => $currentsite]); ?>
</section>

<?php TemplateHelper::includePartial('_tabs.php', 'pages', compact('currentsite', 'tabstohide')); ?>

<?php
  writeErrors($errors, true);
?>
<form method="post" class="edit-form" enctype="multipart/form-data">
  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <td colspan="2">Galerij</td>
    </tr>
    <tr class="dataTableRow">
      <td class="head">Galerij code</td>
      <td>
        <input type="text" name="code" id="code" value="<?php echo $gallery->code; ?>"/> <span class="asterisk">*</span>
        <?php echo showHelpButton('Een galerij kun je zelf toevoegen op je pagina. Door de galerijcode tussen accolades te plaatsen in de tekst op het bewerk scherm, bijv. {galerij_code}. Op de plaatst waar de code staat word op de website automatisch de gallerij getoond.', 'Gallerij') ?>
      </td>
    </tr>
    <?php if($gallery->id != "") : ?>
      <?php if(Privilege::hasRight('MOD_PAGES_GALLERY_MOVE')): ?>
        <tr class="dataTableRow">
          <td class="head">Verplaatsen</td>
          <td>
            <?php
              $extra = '';
              //don not change type in some situations.
              $isselected = false;
              $extra ='<select name="moveintree" id="moveintree">';
              foreach(Page::getTreeOptions($currentsite->id) as $id=>$name):
                $extra .='<option value="'.$id.'" ';
                if($gallery->page_id==$id) {
                  $extra .= "selected";
                  $isselected = true;
                }
                $extra .='>'.$name.'</option>';
              endforeach;
              $extra .='</select>';
              echo $extra;
              if(!$isselected): ?>
                <script type="text/javascript">
                  $(document).ready(function()  {
                    $("#moveintree").prop("disabled", true);
                  });
                </script>
              <?php endif; ?>
            <?php echo showHelpButton('Hiermee kun je een Gallerij verplaatsen naar een andere pagina. De {gallerijcode} dien je nog wel zelf in te voeren op de nieuwe pagina.', 'Gallerij Verplaatsen') ?>
          </td>
        </tr>
      <?php endif; ?>
      <tr class="dataTableHeadingRow topborder">
        <td colspan="2">Beelden</td>
      </tr>
      <tr class="dataTableRow">
        <td colspan="2">
          <?php require_once("_galleryimages.php"); ?>
        </td>
      </tr>
    <?php endif; ?>
  </table>
  <br/>
  <input type="submit" class="gsd-btn gsd-btn-primary" name="go_save" value="Opslaan" />
  <input type="submit" class="gsd-btn gsd-btn-secondary" name="go_list" value="Opslaan en naar lijst" />
</form>