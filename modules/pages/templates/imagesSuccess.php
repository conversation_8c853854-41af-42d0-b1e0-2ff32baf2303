<?php if (!empty($finished_cropping)):
  //indien er gecropt is dan sluiten popup
  ?>
  <script type="text/javascript">
    $(document).ready(function () {
      if (parent.gsdModalCropper !== undefined) {
        parent.gsdModalCropper.close();
      }

    });
  </script>
  <?php
  return;
endif;
?>
<section class="title-bar">
  <h1>
    <?php echo Navigation::getItem(Navigation::getActivePageId())->getName() ?>
  </h1>

  <?php TemplateHelper::includePartial('_tabs.php', 'siteadmin', ['site' => $currentsite]); ?>
</section>

<?php TemplateHelper::includePartial('_tabs.php', 'pages', compact('currentsite', 'tabstohide')); ?>

<link rel="stylesheet" type="text/css" href="<?php echo URL_INCLUDES ?>fileuploader/client/fileuploader.css"/>
<script src="<?php echo URL_INCLUDES ?>fileuploader/client/fileuploader.js" type="text/javascript"></script>
<?php
  $ckeditor = false;
  if (Config::isdefined('PAGE_IMAGES_CKEDITOR_PAGEIDS') && (Config::get('PAGE_IMAGES_CKEDITOR_PAGEIDS') === true || in_array($page->id, Config::get('PAGE_IMAGES_CKEDITOR_PAGEIDS')))):
    $ckeditor = true;
    ?>
    <?php echo TemplateHelper::includeJavascript('/gsdfw/includes/ckeditor4/ckeditor'); ?>
    <?php echo TemplateHelper::includeJavascript('/gsdfw/includes/jsscripts/ckeditor4'); ?>
    <script type="text/javascript">
      $(document).ready(function () {

        ckeditorInit();
        CKEDITOR.config['contentsCss'] = CKEDITOR.config['contentsCss'].concat([<?php echo getCkeditorStylesheets() ?>]);
        ckeditorScaytNl();
        ckeditorSimple();

        CKEDITOR.config['height'] = 90;
        CKEDITOR.config.disableNativeSpellChecker = false;

        <?php if(Config::get('PAGE_CKEDITOR_FILEBROWSER', true)):
        $_SESSION['filebrowserPath'] = DIR_UPLOADS . 'sites/' . $currentsite->id . '/ckfiles';
        ?>
        CKEDITOR.config['filebrowserBrowseUrl'] = '<?php echo Context::getSiteDomain() . URL_INCLUDES ?>ckeditor4/RichFilemanager/index.html';

        <?php endif; ?>

      });
    </script>
  <?php endif; ?>

<?php writeErrors($errors); ?>

<form method="post" enctype="multipart/form-data">
  <?php if (Config::isdefined("MAX_NUMBER_OF_IMAGES")): ?>
    <?php if ($number_of_images < $allowed_number_of_images):
      echo '<b>U heeft momenteel ' . $number_of_images . ' van de ' . $allowed_number_of_images . ' foto\'s geüpload</b><br/><br/>';
      ?>
      <div id="fileuploader"></div><br/>
      <div id="saveimage" style="display:none;margin-bottom: 15px;">
        <input type="submit" id="saveimage_go" class="gsd-btn gsd-btn-primary" value="Geuploade bestanden plaatsen" name="handleuploads"/>
        <?php if (Config::isdefined('IMAGES_PAGE_WATERMARK') && Config::get('IMAGES_PAGE_WATERMARK')): ?>
          <div style="float: left; padding-top: 2px; padding-right: 20px;"><label><input type="checkbox" name="watermark" value="1" class="checkbox" checked/> Gebruik
              watermerk</label> <?php echo showHelpButton("Gebruik deze optie om wel/niet automatisch een watermerk toe te voegen aan de foto die wordt geupload.", 'Watermerk'); ?>
          </div>
        <?php endif; ?>
      </div>
    <?php
    else:
      echo '<div class="box" style="color: blue;">U heeft uw maximaal aantal te uploaden foto\'s van ' . $allowed_number_of_images . ' bereikt.<br/><br/>Verwijder één of neem contact op met GSD</div>';
      echo '<br/>';
    endif;
    ?>
  <?php else: ?>
    <div id="fileuploader"></div><br/>
    <div id="saveimage" style="display:none;margin-bottom: 15px;">
      <input type="submit" id="saveimage_go" class="gsd-btn gsd-btn-primary" value="Geuploade bestanden plaatsen" name="handleuploads"/>
      <?php if (Config::isdefined('IMAGES_PAGE_WATERMARK') && Config::get('IMAGES_PAGE_WATERMARK')): ?>
        <div style="float: left; padding-top: 2px; padding-right: 20px;"><label><input type="checkbox" name="watermark" value="1" class="checkbox" checked/> Gebruik
            watermerk</label> <?php echo showHelpButton("Gebruik deze optie om wel/niet automatisch een watermerk toe te voegen aan de foto die wordt geupload.", 'Watermerk'); ?>
        </div>
      <?php endif; ?>
    </div>
  <?php endif; ?>
  <input type="button" id="sortablecontrol" name="" value="Sorteren aanzetten" class="gsd-btn gsd-btn-secondary"/>
  <?php echo showHelpButton("Door op 'sorteren aanzetten' te klikken word het mogelijk om de rijen van de tabel te verslepen, en zo de volgorde van de beelden aan te passen.", 'Sorteren'); ?>
  <br/><br/>
  <div id="message"></div>

  <?php if (count($pageimages) == 0): ?>
    <section class="empty-list-state">
      <p><?php echo __('Er zijn geen items gevonden.') ?></p>
    </section>
  <?php else: ?>
    <table id="sortable" class="cat default_table">
      <tr class="dataTableHeadingRow nodrop nodrag">
        <td class="sort-td"></td>
        <td>Foto</td>
        <td>
          Informatie <?php echo showHelpButton('Vul hier een alternatieve en omschrijving tekst in voor uw beeld, dit is meteen goed voor uw vindbaarheid.', 'Alternatieve tekst voor beeld') ?>
          <?php if (count($currentsitelanguages) > 1): ?>
            <div style="display: inline-block;vertical-align: top;">
              <script type="text/javascript">
                $(document).ready(function () {
                  var lang = <?php echo json_encode(LanguageHelper::getLanguagesNL()) ?>;

                  $('a.languageselect').on("click", function (item) {
                    $('a.languageselect img').css('border', '1px solid white');
                    $('div.languagebox').hide();
                    $(this).find("img").css('border', '1px solid blue');
                    $('.content_' + $(this).attr("id")).show();
                    $('#currentlanguage').text(lang[$(this).attr("id")]);
                  });
                  $('#<?php echo $_SESSION['worklanguage'] ?>').trigger("click");
                });
              </script>
              <?php
                foreach ($currentsitelanguages as $llang) {
                  echo '<a href="javascript:void(0);" class="languageselect" id="' . $llang . '"><img alt="' . $llang . '" src="/gsdfw/images/flags/' . $llang . '.png"  style="width: 22px; height: 15px; margin-right: 5px; border: 1px solid white;"></a>';
                }
                echo ' Huidige taal: <span style="vertical-align: top;" id="currentlanguage"></span>';
              ?>
            </div>
          <?php endif; ?>
        </td>
        <?php if ($use_cropping_tool) : ?>
          <td>Crop</td>
        <?php endif; ?>
        <td class="action">Acties</td>
      </tr>
      <?php foreach ($pageimages as $image) : ?>
        <tr class="dataTableRow trhover" id="sort_<?php echo $image->id ?>">
          <td class="sort-td"><span class="fa fa-bars"></span></td>
          <td>
            <a href="<?php echo $image->getUrlOrig($currentsite) ?>?time=<?php echo time(); ?>" class="popupimage" title="klik voor preview...." id="img_<?php echo $image->id ?>">
              <img src="<?php echo $image->getUrlThumb($currentsite) ?>?time=<?php echo time(); ?>" alt="klik voor preview...." style="border-color:#681821; max-height: 150px;"/>
            </a>
          </td>
          <td>
            <?php foreach ($currentsitelanguages as $langkey => $llang): ?>
              <div class="content_<?php echo $llang ?> languagebox" <?php if (count($currentsitelanguages) == 1): ?>style="display: block;"<?php endif; ?>>
                <input type="text" value="<?php echo escapeForInput($image->contents[$llang]->alt) ?>" name="alt[<?php echo $image->id ?>][<?php echo $llang ?>]"
                       placeholder="Alt tekst..." style="margin-bottom: 5px;"/>
                <textarea name="content[<?php echo $image->id ?>][<?php echo $llang ?>]" style="height: 70px; width: 100%;" class="ckeditor"
                          placeholder="Foto omschrijving..."><?php echo $image->contents[$llang]->content ?></textarea>
                <?php if (Config::isdefined('PAGE_IMAGES_URL') && (Config::get('PAGE_IMAGES_URL') == 'ALL' || in_array($page->id, Config::get('PAGE_IMAGES_URL')))): ?>
                  <input type="text" value="<?php echo escapeForInput($image->contents[$llang]->href) ?>" name="href[<?php echo $image->id ?>][<?php echo $llang ?>]"
                         placeholder="Link..."/>
                <?php endif; ?>
              </div>
            <?php endforeach; ?>
          </td>
          <?php if ($use_cropping_tool) : ?>
            <td>
              <a href="<?php echo reconstructQueryAdd(['pageId', 'parent_id', 'id']) . 'action=cropimagetool&type=pageimage&imageid=' . $image->id; ?>" class="qtipa cropper_popup">
                <img src="/gsdfw/images/image_crop.png" alt="Bijsnijden"/>
              </a>
            </td>
          <?php endif; ?>
          <td>
            <?php echo BtnHelper::getRemove(reconstructQuery(['action']) . "action=imagedelete&delid=" . $image->id) ?>
          </td>
        </tr>
      <?php endforeach; ?>
    </table>
    <br/>
    <input type="submit" class="gsd-btn gsd-btn-primary" name="go" value="Opslaan"/>
    <input type="submit" class="gsd-btn gsd-btn-primary" name="go_list" value="Opslaan en naar lijst"/>
  <?php endif ?>

</form>

<script type="text/javascript">

  gsdModalCropper = new GsdModal("cropper-modal");
  gsdModalCropper.setWidth("950px")
  gsdModalCropper.setHeight("700px")
  gsdModalCropper.init();

  $(document).ready(function () {

    new SimpleLightbox(".popupimage", {
      fileExt: false,
    });

    $(".cropper_popup").on("click", function (e) {
      e.preventDefault();
      gsdModalCropper.openIframe($(this).attr("href"), "Bijsnijden");
    });

    $(document).on('gsdModalClose', function (e, modalId) {
      if (modalId === "cropper-modal") {
        window.location.reload();
      }
    })

    gsdRowSorter("<?php echo reconstructQueryAdd(['pageId']) ?>page_id=<?php echo $page->id ?>&action=imagemove&");

    createUploader();

    $(document).on("click", ".deletefile", function (event) {
      $(this).parent().hide().find("input").val("");
      event.preventDefault();
    });

  });

  function createUploader() {
    var uploader = new qq.FileUploader({
      element: $("#fileuploader")[0],
      action: '<?php echo reconstructQuery(["action"]) ?>action=upload',
      //debug: true,
      //extraDropzones: [qq.getByClass(document, 'qq-upload-extra-drop-area')[0]]
      allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
      uploadButtonText: 'Upload bestand(en)',
      cancelButtonText: 'Annuleer',
      failUploadText: 'Upload error',
      messages: {
        //serverError: "Some files were not uploaded, please contact support and/or try again.",
        typeError: "{file} heeft een ongeldige extensie. Alleen {extensions} zijn toegestaan.",
        sizeError: "{file} is te groot, maximum bestandsgrootte is {sizeLimit}.",
        emptyError: "{file} is leeg, selecteer uw bestanden opnieuw.",
        limitError: "U kan maximaal {limit} bestanden uploaden."
      },
      onComplete: function (id, fileName, responseJSON) {
        $('.qq-upload-success').each(function () {
          if ($(this).find(".deletefile").length == 0) {
            $(this).append('<a href="" class="deletefile"><?php echo IconHelper::getCrossSimpleRed() ?></a>')
              .append('<input type="hidden" value="' + fileName + '" name="send[files][]"/>');
          }
        });
        $("#saveimage").show();
        <?php if (!Config::isdefined('IMAGES_PAGE_WATERMARK') || !Config::get('IMAGES_PAGE_WATERMARK')): ?>
        $("#saveimage_go").trigger("click");
        <?php endif; ?>
      }
    });

  }
</script>
