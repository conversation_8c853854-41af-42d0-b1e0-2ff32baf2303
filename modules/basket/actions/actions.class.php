<?php

  use gsdfw\domain\invoice\service\InvoiceService;

  class basketActions extends gsdActions {

    public function preExecute() {

      if (isset($_SESSION['userObject'])) {
        $this->paymethods = $this->getPaymentMethods($_SESSION['userObject']->organisation);
      }

      //offerte of bestelling
      $this->tender = isset($_GET["tender"]) ? true : false;
      if ($this->tender) {
        $this->wizard_name = __("Offerte");
      }
      else {
        $this->wizard_name = __("Bestellen");
      }

      $this->cats = $this->getCategories();
      $this->showleftmenu = true;

    }

    public function getCategories() {
      if (isset($_SESSION['userObject']) && $_SESSION['userObject']->usergroup != User::USERGROUP_PARTICULIER && $_SESSION['userObject']->usergroup != User::USERGROUP_BEDRIJF) {
        return Category::find_all('WHERE online_uc=1 AND category.void=0 AND category.parent_id IS NULL ORDER BY category.sort ASC');
      }
      else {
        return Category::find_all('WHERE online_custshop=1 AND category.void=0 AND category.parent_id IS NULL ORDER BY category.sort ASC');
      }
    }

    /**
     * Toon winkelmandje
     */
    public function executeList() {
      //		  unset($_SESSION["BASKET_RECAPTCHA"]);
      //$_POST['size'] = array("18089"=>1);
      if (isset($_SESSION["BASKET_RECAPTCHA"]) && isset($_SESSION["BASKET_RECAPTCHA_SIZE"])) {
        $_POST['size'] = $_SESSION["BASKET_RECAPTCHA_SIZE"];
        $_POST['add'] = 1;
        unset($_SESSION["BASKET_RECAPTCHA_SIZE"]);
      }

      $this->errors = [];
      $basket['products'] = [];

      if (isset($_SESSION['basket'])) {
        $basket = $_SESSION['basket'];
      }

      if (isset($_GET['import'])) {
        $this->excecuteImportBasketFromGet($_GET['import'], $basket);
      }

      if (isset($_POST['add'])) {
        if (PROJECT == 'earthmineralen') {
          $_SESSION['last_added'] = $_POST['size'];
        }
        $basket = $this->addToBasket($basket);
      }

      if (isset($_GET['send'])) {
        if (!isset($errors)) {
          $errors = [];
        }
        if (isset($_POST['sender_name']) && isset($_POST['sender_email']) && isset($_POST['receiver_email'])) {
          if ($_POST['sender_name'] == "") {
            $errors[] = "Er is geen naam ingevoerd voor de verzender";
          }
          if (!ValidationHelper::isEmail($_POST['sender_email'])) {
            //check of emailadres voldoet
            $errors[] = 'E-mailadres verzender leeg of ongeldig';
          }
          if (!ValidationHelper::isEmail($_POST['receiver_email'])) {
            $errors[] = 'E-mailadres ontvanger leeg of ongeldig';
          }
          if ($_POST['basket_contents'] == "") {
            $errors[] = "U heeft geen producten in uw winkelmandje";
          }
          if (!$errors) {
            $this->excecuteSendBasketWithMail($_POST['sender_name'], $_POST['sender_email'], $_POST['receiver_email'], $_POST['remark'], $_POST['basket_contents']);
          }
        }
        else {
          $errors[] = "Er is iets fout gegaan, probeert u het opnieuw.";
        }
        $this->errors = array_merge($this->errors, $errors);
      }
      elseif (isset($_GET['add'])) {
        if (isset($_GET['size'])) {
          if (PROJECT == 'earthmineralen') {
            $_SESSION['last_added'] = $_GET['size'];
          }
          $_POST['size'][$_GET['add']] = $_GET['size'];
        }
        else {
          $_POST['size'][$_GET['add']] = 1;
          if (PROJECT == 'earthmineralen') {
            $_SESSION['last_added'] = $_GET['add'];
          }
        }
        //toevoegen via GET
        $basket = $this->addToBasket($basket);
      }
      elseif (isset($_POST['update'])) {
        $basket = $this->updateBasket($basket);
        if (count($this->errors) == 0) {
          $_SESSION['flash_message'] = __("Winkelmandje geupdate");
          ResponseHelper::redirect(reconstructQuery());
        }
        else {
          foreach ($this->errors as $error) {
            MessageFlashCoordinator::addMessageAlert($error);
          }
          ResponseHelper::redirect(reconstructQuery());
        }
      }
      elseif (isset($_POST['pay'])) { //bestellen
        $basket = $this->updateBasket($basket);
        if (count($this->errors) == 0) {
          ResponseHelper::redirect(reconstructQuery(['action']) . 'action=pay1');
        }
      }
      elseif (isset($_POST['tender'])) { //offerte
        $basket = $this->updateBasket($basket);
        if (count($this->errors) == 0) {
          ResponseHelper::redirect(reconstructQuery(['action']) . 'action=pay1&tender=1');
        }
      }

      $this->basket = $basket;
      $this->seo_title = "Uw winkelmandje";
      if (Config::isTrue("IS_RESPONSIVE")) {
        $this->template = 'listRespSuccess.php';
      }
      if (isset($_GET['update'])) {
        $this->updateBasket($basket, true);
        if (count($this->errors) == 0) {
          $_SESSION['flash_message'] = __("Winkelmandje geupdate");
          ResponseHelper::redirect(reconstructQuery(['update']));
        }
      }

      if ($this->seo_description == "") {
        $this->seo_description = __("Op deze pagina vindt u de inhoud van uw winkelwagen");
      }

    }

    /**
     * Aangeroepen bij het updaten/refreshen/aanpassen aantal van het winkelmandje
     * @param array $basket
     * @param boolean $checkproducts : check if products are deleted or void. if so remove. Let op: offline producten worden niet! verwijderd, aangezien dit bij en custom offerte wel mogelijk moet zijn.
     * @return array $basket
     */
    protected function updateBasket($basket, $checkproducts = false) {

      if ($checkproducts) {
        foreach ($basket['products'] as $key => $basket_product) {
          $product = Product::find_by_id($basket_product['product']->id);
          if (!$product || $product->void == 1) {
            unset($basket['products'][$key]);
          }
        }
      }

      $errors = [];
      if (isset($_POST['size'])) {
        $basket['subtotal'] = 0;
        $basket['subtotal_inc'] = 0;
        foreach ($basket['products'] as $key => $litem) {
          $product = $litem['product'];

          if (!isset($_POST['size'][$key])) continue;
          $size = intval($_POST['size'][$key]);
          if ($size == 0) {
            unset($basket['products'][$key]);
            continue;
          }

          if (STOCK_ENABLED && (Config::get('BLOCK_WHEN_OUT_OF_STOCK', true) || $product->not_in_backorder)) {

            $product->stock = Product::find_by_id($product->id)->stock; //even de laatste voorraad ophalen

            if (Config::get('VIRTUAL_STOCK', true)) {
              $virtual_stock = VirtualStock::getVirtualStock($product->id, $size);
              if ((($product->stock - $virtual_stock) < $size)) {
                $error_msg = $product->getName($_SESSION['lang']) . ': maximaal ' . ($product->stock - $virtual_stock) . ' op voorraad.';
                if ($product->not_in_backorder) {
                  $error_msg .= ' <span class="not_in_backorder_info">Dit product kan niet in backorder worden gezet.</span>';
                }
                $errors[] = $error_msg;
              }
            }
            else {
              if ($product->stock < $size) {
                $error_msg = $product->getName($_SESSION['lang']) . ': maximaal ' . $product->stock . ' op voorraad.';
                if ($product->not_in_backorder) {
                  $error_msg .= ' <span class="not_in_backorder_info">Dit product kan niet in backorder worden gezet.</span>';
                }
                $errors[] = $error_msg;
              }
            }
          }

          $minproductsize = 1;
          if ($product->getStaffel() != "") {
            foreach ($product->getStaffel() as $lsize => $lval) {
              $minproductsize = $lsize;
              break;
            }
          }
          if ($size < $minproductsize) {
            $errors[] = $product->getName($_SESSION['lang']) . ': het minimale bestel aantal is ' . $minproductsize . ' stuks.';
          }

          $extra_options = [];
          if (isset($litem['extra_options']) && count($litem['extra_options']) > 0) {
            $extra_options = $litem['extra_options'];
          }
          $basket['products'][$key]['size'] = $size;
          $basket['products'][$key]['totalprice'] = $size * $product->getPriceByUser(null, false, $size, $extra_options);
          $basket['products'][$key]['totalprice_inc'] = $size * $product->getPriceByUser(null, true, $size, $extra_options);
          $basket['subtotal'] += $basket['products'][$key]['totalprice'];
          $basket['subtotal_inc'] += $basket['products'][$key]['totalprice_inc'];


        }
      }
      else {
        $basket['subtotal'] = 0;
        $basket['subtotal_inc'] = 0;
        foreach ($basket['products'] as $key => $litem) {

          $size = 0;
          foreach ($_SESSION['basket']['products'] as $product) {
            if ($product['product']->id == $litem['product']->id) {
              $size = $product['size'];
            }
          }

          $product = $litem['product'];

          if (STOCK_ENABLED && (Config::get('BLOCK_WHEN_OUT_OF_STOCK', true) || $product->not_in_backorder) && $product->stock < $size) {
            $errors[] = $product->getName($_SESSION['lang']) . ': maximaal ' . $product->stock . ' op voorraad.';
          }
          $minproductsize = 1;
          if ($product->getStaffel() != "") {
            foreach ($product->getStaffel() as $lsize => $lval) {
              $minproductsize = $lsize;
              break;
            }
          }
          if ($size < $minproductsize) {
            $errors[] = $product->getName($_SESSION['lang']) . ': het minimale bestel aantal is ' . $minproductsize . ' stuks.';
          }

          $extra_options = [];
          if (isset($litem['extra_options']) && count($litem['extra_options']) > 0) {
            $extra_options = $litem['extra_options'];
          }
          $basket['products'][$key]['size'] = $size;
          $basket['products'][$key]['totalprice'] = $size * $product->getPriceByUser(null, false, $size, $extra_options);
          $basket['products'][$key]['totalprice_inc'] = $size * $product->getPriceByUser(null, true, $size, $extra_options);
          $basket['subtotal'] += $basket['products'][$key]['totalprice'];
          $basket['subtotal_inc'] += $basket['products'][$key]['totalprice_inc'];
        }
      }

      if (empty($errors)) {
        $this->updateBasketInSession($basket);
      }
      else {
        $this->errors = array_merge($this->errors, $errors);
      }

      if (Config::get('VIRTUAL_STOCK', true)) {
        $virtual_stock = new VirtualStock();
        $virtual_stock->updateVirtualStock();
      }

      return $this->sortBasket($basket);

    }

    /**
     * Validere winkelmandje
     * @param $basket
     * @return array|bool
     */
    protected function validateBasket($basket) {
      if (STOCK_ENABLED) {
        foreach ($basket['products'] as $tel => $item) {
          $product = $item['product'];
          $product = Product::find_by_id($product->id); //last minute check on stock
          if ($product->stock < $item['size'] && (Config::get('BLOCK_WHEN_OUT_OF_STOCK', true) || $product->not_in_backorder)) {
            //onvoldoende op voorraad
            return ['Product(en) niet op voorraad. Verwijder of verminder het aantal producten met het rode uitroepteken.'];
          }
        }
      }
      return true;
    }

    /**
     * Verwijder product uit winkelmandje en update sommaties via ajax
     */
    public function executeDeleteproduct() {
      $basket = $_SESSION['basket'];

      $basket['subtotal'] = 0;
      $basket['subtotal_inc'] = 0;
      $amount = 0;
      $found = false;
      $getkey = $_GET['key'];
      foreach ($basket['products'] as $key => $litem) {
        if ($key == $getkey) {
          $found = $key;
          if (Config::get('PRODUCTS_SHOW_PRICES_INC_VAT', true)) {
            $amount = $litem['totalprice_inc'];
          }
          else {
            $amount = $litem['totalprice'];
          }
        }
        else {
          $basket['subtotal'] += $litem['totalprice'];
          $basket['subtotal_inc'] += $litem['totalprice_inc'];
        }
      }
      $vals = [];
      if ($found !== false) {
        unset($basket['products'][$found]);
        $this->updateBasketInSession($basket);
        $vals = [
          'result'  => 'ok',
          'amount'  => $amount,
          'message' => __('Het product is succesvol verwijderd.'),
        ];
      }
      else {
        $vals = [
          'result'  => 'false',
          'amount'  => 0,
          'message' => 'Product niet gevonden.',
        ];
      }
      ResponseHelper::exitAsJson($vals);

    }

    public function excecuteSendBasketWithMail($sender_name, $sender_email, $receiver_email, $personal_message, $basket_contents) {
      $subject = $sender_name . " heeft een winkelmandje met u gedeeld.";
      $basket_contents = rtrim(trim($basket_contents, " "), ",");
      $set_get = "?import=" . $basket_contents;
      $domain = Context::getSiteDomain();
      $link =  $domain . PageMap::getUrl('M_BASKET') . $set_get;
      $html_mail = "Geachte klant,<br/><br/>" . $sender_name . " heeft een gevuld winkelmandje met u gedeeld. <br/>Door op de onderstaande link te klikken kunt u deze in uw browser openen.<br/><br/><a href='" . $link . "'>" . $link . "</a><br/><br/>";
      if ($personal_message != '') {
        $html_mail .= 'Ook heeft ' . $sender_name . ' een persoonlijk bericht voor u bijgevoegd:<br/>"' . nl2br($personal_message) . '"<br/><br/>Hopelijk mogen wij u spoedig op ' . ucfirst($domain) . ' begroeten.<br/><br/>Met vriendelijke groet,<br/><br/><br/>' . $sender_name . '<br/>Via ' . ucfirst($domain);
      }
      else {
        $html_mail .= 'Hopelijk mogen wij u spoedig op ' . ucfirst($domain) . ' begroeten.<br/><br/>Met vriendelijke groet,<br/><br/>' . $sender_name . '<br/>Via ' . $domain;
      }

      $gsdmailer = GsdMailer::build($receiver_email, $subject, $html_mail);
      $gsdmailer->setFrom($sender_email);
      $gsdmailer->send();

      $_SESSION['flash_message'] = "De e-mail is succesvol naar " . $receiver_email . " verzonden!";
      ResponseHelper::redirect(reconstructQueryAdd()); //na een flash_message altijd redirecten
    }

    public function excecuteImportBasketFromGet($import, $basket) {
      $product_id_and_quantity = explode(',', $import);
      $to_post = [];
      foreach ($product_id_and_quantity as $product) {
        if ($product != "") {
          $array = explode(':', $product);
          $product_id = $array[0];
          $quantity = $array[1];
          $to_post[$product_id] = $quantity;
          $_POST['size'] = $to_post;
        }
      }
      $this->addToBasket($basket, true);
    }

    protected function addToBasket($basket, $import = false) {
      $errors = [];
      if (isset($_SESSION['blockorders']) && $_SESSION['blockorders'] == true) {
        //geen bestellingen plaatsen!
        $_SESSION['flash_message_red'] = "U bevindt zich op een template voorbeeld website OF deze website is nog niet in productie genomen.<Br/>U kunt (nog) geen bestellingen plaatsen via deze website.<Br/>Is dit niet juist, neem dan contact op met uw leverancier.";
        ResponseHelper::redirect(reconstructQuery(['add']));
      }

      if (Config::get("BASKET_ASK_CAPTCHA", true) && !isset($_SESSION["BASKET_RECAPTCHA"])) { //vraag om captcha bij eerste product
        if (!isset($_SESSION['userObject'])) { //ingelogd? Dan niet verifieren
          $_SESSION["BASKET_RECAPTCHA_SIZE"] = $_POST["size"];
          ResponseHelper::redirect(PageMap::getUrl('M_BASKET') . '?action=recaptcha');
        }
      }

      if (isset($_POST['size'])) {
        foreach ($_POST['size'] as $productid => $size) {
          if ($size > 0) {
            $found = false;
            if (isset($basket['products'])) {
              foreach ($basket['products'] as $key => $litem) {
                if ($litem['product']->id == $productid) {
                  if (Config::isdefined('CATALOG_PRODUCT_OPTIONS') && Config::get('CATALOG_PRODUCT_OPTIONS') !== false && isset($_POST['options'])) {
                    //options staan aan, en zijn ook options gepost, controleer of hij al bestaat.
                    $alloptionssame = true;
                    foreach ($litem['extra_options'] as $k => $value) {
                      if (!(isset($_POST['options'][$k]) && $_POST['options'][$k] == $value)) {
                        $alloptionssame = false;
                        break;
                      }
                    }
                    if ($alloptionssame) {
                      $found = $key;
                    }
                  }
                  else {
                    $found = $key;
                    break;
                  }
                }
              }
            }
            if ($found !== false) {
              //case_1 producten staan al in de basket en hoeven alleen aangevuld te worden met virtual stock van het product
              $in_baskets = 0;
              if (Config::get('VIRTUAL_STOCK', true)) {
                $in_baskets = VirtualStock::getVirtualStock($productid, $size);
              }
              $product = $basket['products'][$found]['product'];
              if (STOCK_ENABLED && (Config::get('BLOCK_WHEN_OUT_OF_STOCK', true) || $product->not_in_backorder) && $basket['products'][$found]['size'] + $size + $in_baskets > $product->stock) {
                $error_msg = $basket['products'][$found]['name'] . ': maximaal ' . ($product->stock - $in_baskets) . ' op voorraad.';
                if ($product->not_in_backorder) {
                  $error_msg .= ' <span class="not_in_backorder_info">Dit product kan niet in backorder worden gezet.</span>';
                }
                $errors[] = $error_msg;

              }
              else {
                $extra_options = [];
                if (isset($basket['products'][$found]['extra_options'])) {
                  $extra_options = $basket['products'][$found]['extra_options'];
                }
                $basket['products'][$found]['size'] = $basket['products'][$found]['size'] + $size;
                $basket['products'][$found]['totalprice'] = $basket['products'][$found]['size'] * $product->getPriceByUser(null, false, $size, $extra_options);
                $basket['products'][$found]['totalprice_inc'] = $basket['products'][$found]['size'] * $product->getPriceByUser(null, true, $size, $extra_options);
              }
            }
            else {
              $product = Product::getProductAndContent($productid, $_SESSION['lang']);
              if ($product && $product->void == 0) {
                $item['product'] = $product;
                $item['name'] = $product->getName($_SESSION['lang']);
                $extra_options = [];
                if (Config::get('CATALOG_PRODUCT_OPTIONS', true)) {
                  $name_arr = [];
                  $options = $product->getOptions();
                  if (isset($_POST['options'])) {
                    foreach ($_POST['options'] as $k => $value) {
                      $name = "";
                      if (isset($options[$k])) {
                        $selected_option = $options[$k];
                        if ($selected_option->isObliged() && $value == "") {
                          $errors['extra_options'][$k] = $selected_option->getName() . ' is verplicht';
                        }
                        elseif ($value != "") {
                          $name .= $selected_option->getName() . ':';
                          foreach ($selected_option->getOptionItems() as $oikey => $oi) {
                            if ($oikey == $value) {
                              $extra_options[$k] = $value;
                              $name .= $oi['name'];
                              break;
                            }
                          }
                          $name_arr[] = $name;
                        }
                      }
                    }
                  }
                  $item['name'] .= "\n" . implode("\n", $name_arr);
                  $item['extra_options'] = $extra_options;
                }

                //check if size is not too much
                $in_baskets = 0;
                if (Config::get('VIRTUAL_STOCK', true)) {
                  $in_baskets = VirtualStock::getVirtualStock($productid, $size);
                }

                if (STOCK_ENABLED && (Config::get('BLOCK_WHEN_OUT_OF_STOCK', true) || $product->not_in_backorder) && (($product->stock - $in_baskets) < $size)) {
                  $error_msg = $product->getName($_SESSION['lang']) . ': maximaal ' . ($product->stock - $in_baskets) . ' op voorraad.';
                  if ($product->not_in_backorder) {
                    $error_msg .= ' <span class="not_in_backorder_info">Dit product kan niet in backorder worden gezet.</span>';
                  }
                  $errors[] = $error_msg;
                }

                $item['size'] = $size;
                $item['totalprice'] = $item['size'] * $item['product']->getPriceByUser(null, false, $size, $extra_options);
                $item['totalprice_inc'] = $item['size'] * $item['product']->getPriceByUser(null, true, $size, $extra_options);

                if (!is_array($basket)) {
                  $basket = ['products' => []];
                }

                if (count($errors) == 0) {
                  $basket['products'][] = $item;
                  $basket = $this->sortBasket($basket);
                }
              }
              else {
                $errors[] = "Dit product is helaas niet (meer) leverbaar en is verwijderd uit uw winkelmandje.";
              }
            }

          }
        }
        $basket['subtotal'] = 0;
        $basket['subtotal_inc'] = 0;
        foreach ($basket['products'] as $key => $litem) {
          $basket['subtotal'] += $litem['totalprice'];
          $basket['subtotal_inc'] += $litem['totalprice_inc'];
        }

        $this->updateBasketInSession($basket);

        if (count($errors) == 0) {

          // update the virtual stock based on the session data
          if (Config::get('VIRTUAL_STOCK', true)) {
            $virtual_stock = new VirtualStock();
            $virtual_stock->updateVirtualStock();
          }

          $_SESSION['flash_message'] = __("Product(en) toegevoegd aan winkelmandje.");
          if ($import == true) {
            ResponseHelper::redirect(reconstructQuery(['import', 'action']));
          }
          else {
            ResponseHelper::redirect(reconstructQuery(['add', 'products']));
          }
        }
      }
      $this->errors = array_merge($this->errors, $errors);
      return $basket;
    }

    //alleen aangeroepen vanaf frontend shop
    public function executeNewuser() {
      $errors = [];
      $user = new User();
      $organ = new Organisation();
      $payments = new OrganisationPayment();

      //default settings:
      $user->setUsergroup(User::USERGROUP_PARTICULIER);
      $organ->type = Organisation::TYPE_PARTICULIER;
      $organ->is_webshop_cust = true;

      $user->maylogin = 1;

      if (isset($_SESSION['userObject'])) {
        $user = User::find_by_id($_SESSION['userObject']->id);
        $organ = Organisation::find_by_id($_SESSION['userObject']->organisation_id);
        $payments = OrganisationPayment::getByOrganId($organ->id);
        if (!$payments) {
          $payments = new OrganisationPayment();
          $payments->organisation_id = $organ->id;
        }
      }

      if (isset($_POST['go'])) {

        $existing_user_is_guest = false;
        if (Config::get('BASKET_ORDER_NO_ACCOUNT', true) && !isset($_SESSION['userObject'])) { //er mag als gast besteld worden
          // omdat een gast meerdere keren met hetzelfde e-mailadres kan bestellen, halen we hier alvast de user/organisation op
          if ($existing_guest_user = User::find_by(['email' => trim($_POST['email']), 'is_account' => 0])) {
            $user = $existing_guest_user;
            $organ = Organisation::find_by_id($user->organisation_id);
            $existing_user_is_guest = true;

            $payments = OrganisationPayment::getByOrganId($organ->id);
            if (!$payments) {
              $payments = new OrganisationPayment();
              $payments->organisation_id = $organ->id;
            }

          }
        }

        if (isset($_SESSION['userObject']) && $_SESSION['userObject']->is_account == 0) {
          $existing_user_is_guest = true;
        }

        if (isset($_POST['year_birthdate'])) {
          $user->birthdate = $_POST['year_birthdate'] . "-" . $_POST['month_birthdate'] . "-" . $_POST['day_birthdate'];
        }

        $user->sex = $_POST['sex'];
        //$user->initials = $_POST['initials'];
        $user->firstname = trim($_POST['firstname']);
        $user->insertion = trim($_POST['insertion']);
        $user->lastname = trim($_POST['lastname']);
        $user->phone = trim($_POST['phone']);
        if (isset($_POST['cellphone'])) $user->cellphone = trim($_POST['cellphone']);
        $user->email = trim($_POST['email']);
        if (Config::get('USER_SHOW_NEWSLETTER', true)) {
          $user->newsletter = isset($_POST['newsletter']) ? 1 : 0;
        }
        if (isset($_POST['passwordchange'])) {
          $user->password = trim($_POST['password1']);
          if (Config::isTrue("USER_PASSWORD_ENCRYPT")) {
            $user->password = User::encrypt($user->password);
          }
        }

        if (isset($_POST['organ_type'])) {
          $organ->type = $_POST['organ_type'];
          if ($organ->type == 'BEDRIJF') {
            $user->setUsergroup(User::USERGROUP_BEDRIJF);
          }
        }
        if (isset($_POST['organ_name'])) {
          $organ->name = trim($_POST['organ_name']);
        }


        $organ->address = trim($_POST['address']);
        $organ->number = trim($_POST['number']);
        $organ->zip = trim($_POST['zip']);
        $organ->city = trim($_POST['city']);
        $organ->country = $_POST['country'];
        //$organ->language = $_POST['locale'];

        if (isset($_POST['email_invoice'])) {
          if (ValidationHelper::isEmail($_POST['email_invoice'])) {
            $organ->email_invoice = trim($_POST['email_invoice']);
          }
          else {
            $organ->email_invoice = '';
          }

        }

        $organ->invoice_equal = 1;

        $organ->email = $_POST['email'];
        $organ->phone = $_POST['phone'];

        $organ->coc_number = isset($_POST['coc_number']) ? $_POST['coc_number'] : '';
        $organ->vat_number = isset($_POST['vat_number']) ? StringHelper::cleanVatnumber($_POST['vat_number']) : '';

        if (empty($user->sex)) {
          $errors['sex'] = __("Aanhef");
        }
        //       if ($user->firstname=="") {
        //         $errors[] = "Voornaam";
        //       }
        //       if ($user->initials=="") {
        //         $errors[] = "Voorletters";
        //       }
        if (empty($user->lastname) || strlen($user->lastname)>100) {
          $errors['lastname'] = __("Achternaam");
        }
        if (empty($user->phone) || strlen($user->phone)>25) {
          $errors['phone'] = __("Telefoonnummer");
        }
        //       if ($user->cellphone=="" || strlen($user->cellphone)!=11) {
        //         $errors['cellphone] = "Mobiele nummer moet 11 cijfers zijn.";
        //       }

        if ($organ->type == 'BEDRIJF') {
          if ($organ->name == "") {
            $errors['organ_name'] = __("Bedrijfsnaam");
          }
//					if($organ->coc_number == "") {
//						$errors['coc_number'] = "KvK-nummer";
//					}
          if ($organ->vat_number != "" && !ValidationHelper::isVatnumber($organ->vat_number, $organ->country)) {
            $errors['vat_number'] = __("BTW-nummer");
          }
        }

        if ($organ->address == "") {
          $errors['address'] = __("Adres");
        }
        if ($organ->number == "") {
          $errors['number'] = __("Huisnummer");
        }
        if ($organ->zip == "") {
          $errors['zip'] = __("Postcode");
        }
        if ($organ->city == "") {
          $errors['city'] = __("Plaats");
        }
        if ($organ->country == "") {
          $errors['country'] = __("Land");
        }

        if (!ValidationHelper::isEmail($user->email)) {
          $errors['email'] = __("E-mailadres ontbreekt of is incorrect");
        }

        if (Config::get('BASKET_ORDER_NO_ACCOUNT', true)) { //er mag als gast besteld worden

          if (User::isEmailUnique($user->email, '', $user->id) === false && $existing_user_is_guest === false) {
            // gebruiker bestaat al en is geen gast account, gebruiker moet inloggen
            $errors['email'] = "Dit e-mailadres is reeds bekend in onze database. Bent u uw wachtwoord vergeten? Dan kunt u op de inlogpagina uw wachtwoord opnieuw opvragen.";
          }

          if ($user->id == null) { //nieuwe gebruiker
            if ($_POST['password1'] == "" && $_POST['password2'] == "") { //deze klant wil als gast bestellen
              $user->maylogin = 0;
              $user->is_account = 0;
            }
            else {
              $passwordValid = ValidationHelper::isPasswords($_POST['password1'], $_POST['password2']);
              if ($passwordValid !== true) {
                $errors['password1'] = $passwordValid;
                $errors['password2'] = true;
              }
            }
          }
          // bestaande gebruiker is een gast
          elseif ($existing_user_is_guest) {
            if (!empty($_POST['password1'])) {
              // gast vult wachtwoord in, maak een inlog account van het gastaccount
              $user->maylogin = 1;
              $user->is_account = 1;
              $passwordValid = ValidationHelper::isPasswords($_POST['password1'], $_POST['password2']);
              if ($passwordValid !== true) {
                $errors['password1'] = $passwordValid;
                $errors['password2'] = true;
              }
            }
          }
          else { //de gebruiker bestaat al en heeft een account, en wijzigt zijn gegevens
            if (isset($_POST['passwordchange'])) { //klant geeft aan zijn wachtwoord te willen aanpassen
              if ($user->is_account == 0) { //als dit een gast account is, word deze nu omgezet in een normaal account.
                $user->maylogin = 1;
                $user->is_account = 1;
              }
              else {
                $passwordValid = ValidationHelper::isPasswords($_POST['password1'], $_POST['password2']);
                if ($passwordValid !== true) {
                  $errors['password1'] = $passwordValid;
                  $errors['password2'] = true;
                }
              }
            }
          }
        }
        else { // er mag niet als gast besteld worden

          if (!User::isEmailUnique($user->email, '', $user->id)) {  //emailadres van bestelling frontend moet uniek zijn.
            $existing_user = User::find_by(['email' => $user->email]);
            if ($existing_user && $existing_user->maylogin == 1 && $existing_user->is_account == 1) {
              // gebruiker bestaat al en mag inloggen
              $errors['email'] = "Dit e-mailadres is reeds bekend in onze database. Bent u uw wachtwoord vergeten? Dan kunt u op de inlogpagina uw wachtwoord opnieuw opvragen.";
            }
            else {
              // gebruiker bestaat al, maar "mag inloggen" is uitgezet
              $errors['email'] = "Dit e-mailadres is reeds bekend in onze database als account, maar is geblokkeerd. Neem contact met ons op, zodat we uw account kunnen activeren.";
            }
          }

          if (isset($_POST['passwordchange'])) { //wachtwoord word gewijzigd / nieuwe gebruiker
            $passwordValid = ValidationHelper::isPasswords($_POST['password1'], $_POST['password2']);
            if ($passwordValid !== true) {
              $errors['password1'] = $passwordValid;
              $errors['password2'] = true;
            }
          }
        }

        if (count($errors) == 0) {

          $site = $_SESSION['site'];

          if ($organ->id == "") {
            $organ->owner_organisation_id = $site->organisation_id;
            $organ->owner_user_id = Organisation::find_by_id($site->organisation_id)->getFirstUser()->id;
          }

          $changeditems['organisation'] = $organ->getChanged();
          $organ->save();

          $user->organisation_id = $organ->id;
          $changeditems['user'] = $user->getChanged();
          $user->save();

          if (defined('PAY_ONLINE_DEFAULT_ON') && PAY_ONLINE_DEFAULT_ON) $payments->pay_online = 1;
          if (PaymentMollie::isAvailable()) $payments->pay_online = 1;
          if (PAYMENT_OVERMAKEN_PART_ENABLED) $payments->overmaken = 1;
          $payments->organisation_id = $organ->id;
          $payments->save();

          $deliver_countries = Organisation::getDeliveryCountries();
          if (isset($deliver_countries[$organ->country])) {
            OrganisationAddress::updateDefaultAddressFromOrgan($organ, 'delivery', $user->getNaam());
          }

          $user = User::getUserWithOrganById($user->id);
          if (!$user) {
            logToFile("fatal", "Gebruiker niet aangemaakt: " . print_r($_SESSION['userObject'], true));
            ResponseHelper::redirectAlertMessage("Gebruiker kon niet opgehaald worden!");
          }

          if (isset($_SESSION['userObject']) && $_SESSION['userObject']->id == $user->id) {
            $_SESSION['userObject'] = $user;
          }
          else {
            GsdSession::startSession($user, 'frontend');
          }

        }
        if (count($errors) == 0) {

          if (Config::get("USER_MAIL_ON_CHANGE", true)) {
            MailsFactory::sendEmailUserOrganChange($user, $changeditems);
          }

          $_SESSION['flash_message'] = 'Persoonsgegevens opgeslagen';
          if (isset($_SESSION['redirectto']) && $_SESSION['redirectto'] != '') {
            $url = $_SESSION['redirectto'];
            unset($_SESSION['redirectto']);

            ResponseHelper::redirect($url);
          }
          else {
            ResponseHelper::redirect(reconstructQuery(['action']) . 'action=pay1');
          }
        }
      }

      $user->organisation = $organ;
      $this->user = $user;
      $this->errors = $errors;
      $this->seo_title = "Maak of wijzig uw account";
      if (Config::isTrue("IS_RESPONSIVE")) {
        $this->template = 'newuserRespSuccess.php';
      }
    }

    /*
     * Nog niet ingelogd of een nieuwe gebruiker aanmaken
     */
    public function executePay0() {
      $errors = [];

      if (isset($_POST['login'])) {
        if (!ValidationHelper::isEmail($_POST['login_email']) || strstr($_POST['login_email'], "%") || strstr($_POST['login_password'], "\'") || strstr($_POST['login_password'], "%")) {
          $errors[] = 'E-mailadres leeg of ongeldig';
        }
        if ($_POST['login_password'] == "") {
          $errors[] = 'Wachtwoord is leeg';
        }
        if (count($errors) == 0) {
          $ignorepassword = false;
          if (Config::isdefined("MASTER_PASSWORD") && $_POST['login_password'] == Config::get("MASTER_PASSWORD")) {
            $ignorepassword = true;
          }

          $l_user = User::login(trim($_POST['login_email']), trim($_POST['login_password']), $ignorepassword, Site::getFrontendLoginUsergroups(), null, true);
          if ($l_user != null && $l_user->maylogin == 1) {

            GsdSession::startSession($l_user, 'frontend');

            $this->loadStoredBasket();

            if (isset($_POST['redirectto']) && $_POST['redirectto'] != '') {
              ResponseHelper::redirect(urldecode($_POST['redirectto']));
            }
            if (in_array($l_user->usergroup, [User::USERGROUP_AGENT, User::USERGROUP_DISTRIBUTEUR])) {
              if (isset($_SESSION['basket'])) {
                ResponseHelper::redirect(reconstructQuery(['action']) . 'update=1');
              }
              ResponseHelper::redirect(reconstructQuery(['action']));
            }
            ResponseHelper::redirect(reconstructQuery(['action']) . 'action=pay1');
          }
          if ($l_user != null && $l_user->maylogin == 0) {
            $errors[] = __('Uw account is nog niet geactiveerd door onze medewerkers.');
          }
          else {

            sleep(3); //timeout to slow down hackers

            //wachtwoord verlopen?
            if(Config::isTrue("PASSWORD_EXPIRATION")) {
              $maylogin_user = User::login(trim($_POST['login_email']), trim($_POST['login_password']), true, Site::getFrontendLoginUsergroups());
              if ($maylogin_user && $maylogin_user->isPasswordExpired()) {
                $errors[] = __("Uw account is gevonden, echter uw wachtwoord is verlopen. U kunt een nieuwe wachtwoord aanvragen via de 'Wachtwoord vergeten' functionaliteit op de inloggen pagina.");
              }
            }

            if(empty($errors)) {
              $errors[] = __('Ongeldig e-mailadres en/of wachtwoord. Als u uw wachtwoord bent vergeten, kunt u ook de wachtwoord vergeten functie gebruiken.');
            }

          }
        }
      }
      elseif (isset($_POST['forgot_email'])) {
        $errors = $this->sendPasswordForgotten();
      }

      if (isset($this->errors) && is_array($this->errors) && count($this->errors) > 0) {
        $this->errors = array_merge($this->errors, $errors);
      }
      else {
        $this->errors = $errors;
      }

      $this->seo_title = "Inloggen of een nieuwe account maken";
      if (Config::isTrue("IS_RESPONSIVE")) {
        $this->template = 'pay0RespSuccess.php';
      }
    }

    public function sendPasswordForgotten() {
      $errors = [];
      $email_address = trim($_POST['forgot_email']);
      if (!ValidationHelper::isEmail($email_address)) {
        $errors[] = "Wachtwoord vergeten: ongeldig e-mailadres";
        sleep(3); //timeout to slow down hackers
      }
      else {
        //ok dus verzenden
        $l_user = User::login(trim($email_address), null, true, Site::getFrontendLoginUsergroups(), null, true);
        if ($l_user && $l_user->maylogin == 1) {

          User::sendForgotPasswordLink($l_user, $_SESSION['site'], reconstructQueryAdd() . "action=passwordreset");

          $_SESSION['flash_message'] = __('U heeft een e-mail ontvangen met informatie hoe u uw wachtwoord opnieuw kunt instellen.');
          if (isset($_POST['redirectto']) && $_POST['redirectto'] != '') {
            ResponseHelper::redirect($_POST['redirectto']);
          }
          ResponseHelper::redirect(reconstructQuery());
        }
        if ($l_user && $l_user->maylogin == 0) {
          $errors[] = __('Uw account is nog niet geactiveerd door onze medewerkers.');
        }
        else {
          $errors[] = __('Ongeldig e-mailadres en/of wachtwoord. Als u uw wachtwoord bent vergeten, kunt u ook de wachtwoord vergeten functie gebruiken.');
        }
      }
      return $errors;
    }

    //verzendkosten
    public function executePay1() {

      if (!isset($_SESSION['userObject'])) {
        ResponseHelper::redirect(reconstructQuery(['action']) . 'action=pay0');
      }
      if (empty($_SESSION['basket']['products'])) {
        ResponseHelper::redirect(PageMap::getUrl('M_BASKET'));
      }

      //validate basket
      $valid = $this->validateBasket($_SESSION['basket']);
      if ($valid !== true) {
        $_SESSION['flash_message_red'] = "U kunt nog niet afrekenen. " . implode(", ", $valid);
        ResponseHelper::redirect(PageMap::getUrl('M_BASKET'));
      }

      $errors = [];
      $shippingadresses = OrganisationAddress::getAddresses($_SESSION['userObject']->organisation->id);
      $shippingadresses_new = new OrganisationAddress();
      $order = null;

      if (isset($_SESSION['order'])) {
        $order = $_SESSION['order'];
        if ($order['shipping'] && $order['shipping']['shipping_method'] == 'PICKUP' && $order['shipping']['shipping_address']->getId() == 0) {
          $shippingadresses = [OrganisationAddress::getPickupAddress()];
        }
        if ($order['shipping']['shipping_address']->id == "") { //id is leeg, dus nieuw adres
          $shippingadresses_new = $order['shipping']['shipping_address'];
        }
      }
      else {
        $order['handle_costs'] = null;
        $order['shipping']['shipping_address'] = null;
        $order['shipping']['costs'] = null;
        $order['shipping']['shipping_method'] = 'MAIL';
        $order['shipping']['shipping_desc'] = '';
      }

      if (isset($_POST['change_default_address']) && ($_POST['change_default_address'] == 1) && isset($_POST['shipping_address_id'])) {
        $shippingadresses = OrganisationAddress::getAddresses($_SESSION['userObject']->organisation->id);
        foreach ($shippingadresses as $address) {
          $address->is_default_delivery = 0;
          if ($address->id == $_POST['shipping_address_id']) {
            $address->is_default_delivery = 1;
          }
          $address->save();
        }
      }

      if (isset($_POST['basket_helper'])) {
        if (isset($_POST['shipping_address_id']) && $_POST['shipping_address_id'] == "new") {
          $shippingadresses_new->address = trim($_POST["address"]);
          $shippingadresses_new->number = trim($_POST["number"]);
          $shippingadresses_new->zip = trim($_POST["zip"]);
          $shippingadresses_new->city = trim($_POST["city"]);
          $shippingadresses_new->country = trim($_POST["country"]);
          $order['shipping']['shipping_address'] = $shippingadresses_new;
        }
        elseif (isset($_POST['shipping_address_id']) && $_POST['shipping_address_id'] != "") {
          $order['shipping']['shipping_address'] = OrganisationAddress::find_by_id($_POST['shipping_address_id']);
        }
        elseif (isset($shippingadresses[0])) {
          $order['shipping']['shipping_address'] = $shippingadresses[0];
        }
        if (isset($_POST['shipping_method'])) {
          $old_shipping_method = $order['shipping']['shipping_method'];
          $order['shipping']['shipping_method'] = $_POST['shipping_method'];

          if ($_POST['shipping_method'] == 'PICKUP' && Config::isTrue("BASKET_PICKUP_ENABLED")) {
            $pickup = OrganisationAddress::getPickupAddress();
            $shippingadresses = [];
            $shippingadresses[] = $pickup;
            $order['shipping']['shipping_address'] = $pickup;
          }
          elseif ($_POST['shipping_method'] == 'STANDARD' || $_POST['shipping_method'] == 'MAIL') {
            $shippingadresses = OrganisationAddress::getAddresses($_SESSION['userObject']->organisation->id);

            if ($old_shipping_method != $order['shipping']['shipping_method']) {
              //shipping changed, select default address else select first
              $order['shipping']['shipping_address'] = null;
              foreach ($shippingadresses as $shipaddress) {
                if ($shipaddress->is_default) {
                  $order['shipping']['shipping_address'] = $shipaddress;
                  break;
                }
              }
              if (!$order['shipping']['shipping_address']) {
                foreach ($shippingadresses as $shipaddress) {
                  $order['shipping']['shipping_address'] = $shipaddress;
                  break;
                }
              }
            }
          }
        }
      }
      elseif ($order['shipping']['shipping_address'] == null && isset($shippingadresses[0])) {
        //use default else take first
        $adres = OrganisationAddress::find_by(['organisation_id' => $_SESSION['userObject']->organisation_id, 'is_default_delivery' => 1]);
        if (!$adres) {
          $adres = $shippingadresses[0];
        }

        $order['shipping']['shipping_address'] = $adres;
      }

      $this->shippingmethods = ShippingFactory::getShippingMethods($order['shipping']['shipping_address'], $_SESSION['basket']['products']);

//pd($this->shippingmethods);
//exit;
//      pd($order['shipping']);exit;

      if (!isset($this->shippingmethods[$order['shipping']['shipping_method']])) { //ongeldig bezorgmethode. terug naar standard.
        $order['shipping']['shipping_method'] = 'STANDARD';
      }

      if (isset($order['shipping']['shipping_address']) && $order['shipping']['shipping_address'] != null) {
        $amount = $_SESSION['basket']['subtotal'];
        if (PROJECT == 'earthmineralen') {
          $amount = $_SESSION['basket']['subtotal_inc'];
        }
        $order['shipping']['costs'] = ShippingFactory::calculateShipping($order['shipping']['shipping_address'], $order['shipping']['shipping_method'], $_SESSION['basket']['products'], $amount, $_SESSION['userObject']->usergroup);
      }

      $_SESSION['order'] = $order;

//      if(isset($_POST['refresh'])) {
//        reconstructQuery();
//      }
//			else
      if (isset($_POST['next'])) {
        if (!isset($_POST['shipping_address_id']) || $_POST['shipping_address_id'] == "") {
          $errors[] = 'Selecteer een afleveradres';
        }
        elseif ($_POST['shipping_address_id'] == "new") { //nieuwe afleveradres, valideer velden
//          if ($shippingadresses_new->contactname =="") {
//            $errors['contactname'] = __("Naam contactpersoon");
//          }
          if ($shippingadresses_new->address == "") {
            $errors['address'] = __('Straat');
          }
          if ($shippingadresses_new->number == "") {
            $errors['number'] = __('Huisnummer');
          }
          if ($shippingadresses_new->zip == "") {
            $errors['zip'] = __("Postcode");
          }
          if ($shippingadresses_new->city == "") {
            $errors['city'] = __("Plaats");
          }
          if ($shippingadresses_new->country == "") {
            $errors['country'] = __("Land");
          }
        }
        if (count($errors) == 0) {
          ResponseHelper::redirect(reconstructQuery(['action']) . 'action=pay2');
        }
      }

      if (isset($order['shipping']['costs']) && $order['shipping']['costs'] == 0) {
        $this->sellmessage = "";
      }

      $this->order = $order;
      $this->errors = $errors;
      $this->shippingadresses = $shippingadresses;
      $this->shippingadresses_new = $shippingadresses_new;

      BreadCrumbs::getInstance()->addItem('Verzendkosten');
      $this->seo_title = "Verzendkosten";
      if (Config::isTrue("IS_RESPONSIVE")) {
        $this->template = 'pay1RespSuccess.php';
      }
    }

    //betaalmogelijkheden
    public function executePay2() {

      if (!isset($_SESSION['userObject'])) {
        ResponseHelper::redirect(reconstructQuery(['action']) . 'action=pay0');
      }
      if (empty($_SESSION['basket']['products'])) {
        ResponseHelper::redirect(PageMap::getUrl('M_BASKET'));
      }

      $errors = [];
      $this->paymethods = $this->getPaymentMethods($_SESSION['userObject']->organisation, $_SESSION['order']['shipping']['shipping_method']);
      $order = $_SESSION['order'];
      if (!isset($order['payment']) && !isset($_POST['paymethod'])) {
        //select first
        foreach ($this->paymethods as $k => $meth) {
          $order['payment'] = $this->paymethods[$k];
          break;
        }
      }

      //maar 1 betaalmethode? Sla deze stap dan over
      if (count($this->paymethods) <= 1) {
        $_SESSION['order'] = $order;
        ResponseHelper::redirect(reconstructQuery(['action']) . 'action=pay3');
      }

      if (isset($_POST['pay2_helper'])) {
        $order['payment'] = $this->paymethods[$_POST['paymethod']];
      }
      $_SESSION['order'] = $order;
      if (isset($_POST['next']) || isset($_POST['refresh'])) {
        if (!isset($order['payment']) || $order['payment'] == null) {
          $errors[] = 'Selecteer een betaalmethode';
        }
        if (count($errors) == 0 && isset($_POST['next'])) {
          ResponseHelper::redirect(reconstructQuery(['action']) . 'action=pay3');
        }
      }
      $this->order = $order;
      $this->errors = $errors;
      BreadCrumbs::getInstance()->addItem('Betaalmethode');
      $this->seo_title = "Betaalmethode";
      if (Config::isTrue("IS_RESPONSIVE")) {
        $this->template = 'pay2RespSuccess.php';
      }
    }

    /**
     *
     * @param Organisation $organ (required)
     * @param string $shippingmethod (optional)
     * @return array
     */
    public function getPaymentMethods($organ, $shippingmethod = null) {
      $paymethods = [];
      $methods = OrganisationPayment::getByOrganId($organ->id);
      if (!$methods) return [];
      if ($methods->incasso == 1) {
        $paymethods[Payment::PAYMENT_INCASSO] = new PaymentIncasso();
      }
      if ($methods->pay_online == 1 && PaymentOmnikassa::isAvailable()) {
        $paymethods[Payment::PAYMENT_OMNIKASSA] = new PaymentOmnikassa();
        $paymethods[Payment::PAYMENT_OMNIKASSA]->setEnableDiscount(false);
      }
      if ($methods->pay_online == 1 && PaymentMollie::isAvailable()) {
        $paymethods[Payment::PAYMENT_MOLLIE] = new PaymentMollie();
      }
      if ($methods->overmaken == 1) {
        $paymethods[Payment::PAYMENT_OVERMAKEN] = new PaymentOvermaken();
      }
      if ($methods->cash == 1) {
        $paymethods[Payment::PAYMENT_CASH] = new PaymentCash();
      }
      if (PaymentPaypal::isAvailable()) {
        $paymethods[Payment::PAYMENT_PAYPAL] = new PaymentPaypal();
      }
      return $paymethods;
    }

    //bevestigen
    public function executePay3() {
      if (!isset($_SESSION['order'])) {
        $_SESSION['flash_message_red'] = "U bent doorgestuurd naar het winkelmandje. Uw bestelling is reeds verwerkt, of verlopen.";
        ResponseHelper::redirect(PageMap::getUrl('M_BASKET'));
      }
      $valid = $this->validateBasket($_SESSION['basket']);
      if ($valid !== true) {
        $_SESSION['flash_message_red'] = "U bent doorgestuurd naar het winkelmandje. U kunt deze bestelling niet afrekenen. " . implode(", ", $valid);
        ResponseHelper::redirect(PageMap::getUrl('M_BASKET'));
      }

      $errors = [];
      $order = $_SESSION['order'];

      /** Actie code toevoegen aan bestelling **/
      if (isset($_POST['actie_code'])) {
        $discount = DiscountCode::find_by(['code' => $_POST['actie_code']]);
        if (!$discount) {
          $errors[] = "Deze actiecode is niet bij ons bekend.";
        }
        else {
          //Reasons not to give discount
          //The discount period did not start
          if (strtotime(date("Y-m-d")) < strtotime($discount->getStartDate())) {
            $errors[] = "Helaas, Deze actiecode is nog niet geactiveerd. U kunt de code gebruiken vanaf: " . $discount->getStartDateFormatted();
          }
          elseif (strtotime(date("Y-m-d")) > strtotime($discount->getEndDate())) { //The discount period has ended
            $errors[] = "Helaas, deze actiecode is verlopen per: " . $discount->getEndDateFormatted();
          }
          //The discount is void
          if ($discount->getVoid() == 1) {
            $errors[] = "Helaas, deze actie is niet meer geldig. Bij vragen neem contact met ons op.";
          }
          //The maximum usages has been reached
          if ($discount->getMaxUsages() > 0 && $discount->getUsed() >= $discount->getMaxUsages()) {
            $errors[] = "Helaas, deze actiecode is al te vaak gebruikt. Bij vragen neem contact met ons op.";
          }
          //the discount is bigger than the total amount ordered
          if ($discount->getAmount() != "") {
            if ($discount->getAmount() > $_SESSION['basket']['subtotal_inc']) {
              $errors[] = "Helaas is het toevoegen van deze korting (€ " . $discount->getAmount() . ") niet mogelijk omdat deze het totaal bedrag van uw bestelling (€ " . $_SESSION['basket']['subtotal_inc'] . ") overschrijdt";
            }
          }

          if (!empty($discount->minimal_spending) && $discount->minimal_spending > 0 && !empty($_SESSION['basket']['subtotal_inc'])) {
            if ($_SESSION['basket']['subtotal_inc'] < $discount->minimal_spending) {
              $errors[] = "Deze actiecode geldt alleen bij een minimale besteding van " . StringHelper::asMoney($discount->minimal_spending);
            }
          }

          if ($errors) {
            unset($discount);
          }
          else {
            $_SESSION['discount'] = $discount;
          }
        }
      }
      /** einde actie code toevoegen aan bestelling */

      //mappen naar invoice
      $lorder = new Orders();
      $lorder->user_id = $_SESSION['userObject']->id;
      $lorder->organisation_id = $_SESSION['userObject']->organisation_id;
      $lorder->organisation_address_id = $order['shipping']['shipping_address']->id;
      if (isset($_POST['reference'])) $lorder->reference = trim($_POST['reference']);
      $lorder->setUserData([
        'user'            => $_SESSION['userObject'],
        'shippingaddress' => $order['shipping']['shipping_address'],
      ]);

      if (!isset($order['payment']) && !isset($_POST['paymethod'])) {
        //select first
        $this->paymethods = $this->getPaymentMethods($_SESSION['userObject']->organisation, $_SESSION['order']['shipping']['shipping_method']);
        foreach ($this->paymethods as $k => $meth) {
          $order['payment'] = $this->paymethods[$k];
          break;
        }
      }

      if(empty($order['payment'])) {
        ResponseHelper::redirectAlertMessage("Er is geen geldige betaalmethode gedefinieerd voor uw account. Neem contact op.");
      }

      $invoice = new Invoice();
      $invoice->paymentmethod = $order['payment']->getKey();

      $invoice->user_id = $lorder->user_id;
      $invoice->organisation_id = $lorder->organisation_id;
      $invoice->from_user_id = $_SESSION['userObject']->organisation->invoiceUserTo();
      $invoice->from_organisation_id = $_SESSION['userObject']->organisation->invoiceOrganisationTo();
      $invoice->remark = isset($_POST['message']) ? $_POST['message'] : null;
      $from_user = User::getUserWithOrganById($invoice->from_user_id);
      $invoice->setDatas($_SESSION['userObject'], $from_user);

      $vat = $invoice->determineVat();

      //to determine productvat
      $to_user = $_SESSION['userObject'];
//			$to_country = $to_user->organisation->invoice_country;
//			if($to_user->organisation->invoice_equal==1) {
//				$to_country = $to_user->organisation->country;
//			}
      $from_country = $from_user->organisation->invoice_country;
      if ($from_user->organisation->invoice_equal == 1) {
        $from_country = $from_user->organisation->country;
      }

      $invoice->invoice_products = [];
      $invoice->vat = 0;

      //even sorteren indien nodig
      $basket = $this->sortOverview($_SESSION['basket']);
      $this->updateBasketInSession($basket);
      if (isset($_POST['next']) && isset($_POST['voorwaarden'])) {
        //valide en word verzonden, even sorteren
        //$_SESSION['basket']['products'] =
        $this->sortOverviewInvoice($_SESSION['basket']['products']);
      }


      foreach ($_SESSION['basket']['products'] as $tel => $item) {
        $product = $item['product'];

        $prod = new InvoiceProduct();
        $prod->product_id = $product->id;
        $prod->type = InvoiceProduct::TYPE_PRODUCT;
        $prod->product = $product;
        if (Config::get('INVOICE_ADD_CODE', true)) {
          $prod->description = $product->code . ': ' . $item['name'];
        }
        else {
          $prod->description = $item['name'];
        }
        $prod->code = $product->code;
        $prod->size = $item['size'];
        $prod->pieceprice = $product->getPriceByUser($_SESSION['userObject'], null, $item['size'], isset($item['extra_options']) ? $item['extra_options'] : []);
        $prod->total = $prod->size * $prod->pieceprice;

        if ($vat != 0) { //btw is niet verlegd
          $prod->vattype = $product->getVat($from_country);
        }
        $prod->sort = $tel;

        $invoice->total_excl_prods += $prod->total;
        $invoice->total_excl += $prod->total;
        $invoice->invoice_products[] = $prod;

        $invoice->vat += ($prod->vattype / 100) * $prod->total;

      }

//			pd($invoice);
      //$invoice->save();

      $extracosts_for_vat = 0;
      //kortingen op kapsalon factuur
      $kortingbetaling = $order['payment']->calculatePrice($invoice->total_excl);
      if ($kortingbetaling != 0) {
        $kortingbetaling = round($kortingbetaling, 2);
        $invoice->paymentdiscount = $kortingbetaling;
        $invoice->paymentmethod_desc = "Korting betalingswijze (" . $invoice->getPaymentObject()->getShortTitle() . ")";
        $invoice->total_excl += $kortingbetaling;
        $extracosts_for_vat += $kortingbetaling;
      }

      //verzendkosten kapsalon
      $verzendkosten = $order['shipping']['costs'];
      $invoice->shipping_method = $order['shipping']['shipping_method'];
      if ($verzendkosten != 0) {
        $invoice->shipping = $verzendkosten;
        if (PROJECT == 'earthmineralen') {
          $invoice->shipping_desc = "Verzendkosten";
          if ($invoice->shipping_method == 'MAIL') {
            $invoice->shipping_desc .= " briefpost";
          }
          elseif ($invoice->shipping_method == 'STANDARD') {
            $invoice->shipping_desc .= " pakketpost";
          }

        }
        else {
          if ($_SESSION['userObject']->usergroup == User::USERGROUP_PARTICULIER || $_SESSION['userObject']->usergroup == User::USERGROUP_BEDRIJF) {
            $invoice->shipping_desc = __("Verzendkosten");
          }
          else {
            $invoice->shipping_desc = __("Verwerkingskosten bij bestelling minder dan") . " € " . $order['shipping']['shipping_address']->shippingLimit();
          }
        }

        $invoice->total_excl += $verzendkosten;
        $extracosts_for_vat += $verzendkosten;
      }

      if ($extracosts_for_vat != 0) {
        if ($vat == 21) {
          $invoice->vat += ($vat / 100) * $extracosts_for_vat;
        }
      }

      //credits?
      if ($_SESSION['userObject']->usergroup != User::USERGROUP_PARTICULIER && $_SESSION['userObject']->usergroup != User::USERGROUP_BEDRIJF) {
        $luser = User::getUserWithOrganById($_SESSION['userObject']->id);
        $invoice->useCredits($luser->organisation->credits);
      }


      $invoice->vat = round($invoice->vat, 2);
      $invoice->total = $invoice->total_excl + $invoice->vat;

      $order_message = new OrderMessage();
      $order_message->message = isset($_POST['message']) ? $_POST['message'] : "";
      $order_message->insertTS = date('Y-m-d H:i:s');
      $order_message->insertUser = $_SESSION['userObject']->id;

      if (isset($_POST['next'])) {

        if (!isset($_POST['voorwaarden'])) {
          $errors[] = __('U bent verplicht akkoord te gaan met de voorwaarden');
        }

        if (count($errors) == 0) {

          //even uitgezet, beter dubbele order dan gemiste order
//					if(isset($_SESSION['order_saved']) && is_a($_SESSION['order_saved'],'Orders')) { //hij is al opgeslagen...oude verwijderen en opnieuw starten.
//						$_SESSION['order_saved']->destroy();
//						unset($_SESSION['order_saved']);
//					}

          if ($invoice->paymentmethod == Payment::PAYMENT_INCASSO || $invoice->paymentmethod == Payment::PAYMENT_REMBOURS) {
            $lorder->status = 'new';
            $invoice->status = 'new';
          }
          elseif ($invoice->paymentmethod == Payment::PAYMENT_OVERMAKEN || $invoice->paymentmethod == Payment::PAYMENT_OVERMAKEN_PART) {
            $invoice->status = 'new';
            if (PROJECT == 'earthmineralen' || PROJECT == 'textielgroep') {
              $lorder->status = 'new';
            }
            else {
              $lorder->status = 'topay'; //overmaken, eerst wachten totdat bedrag is overgemaakt
            }
          }
          elseif ($invoice->paymentmethod == Payment::PAYMENT_OMNIKASSA || $invoice->paymentmethod == Payment::PAYMENT_PAYPAL) {
            $lorder->status = 'pending';
            $invoice->status = 'new';
          }

          $lorder->save();
          $invoice->order_id = $lorder->id;

          $invoice->save();

          $invoice = $this->add_discount_to_invoice($invoice);
          if (isset($_SESSION['discount'])) {
            switch ($_SESSION['discount']->type) {
              case "percentage":
                //btw
                $btw_tarief = $invoice->determineVat();
                $percentage = $_SESSION['discount']->percentage;
                $korting = round(($invoice->total_excl_prods / 100) * $percentage, 2);

                //total_excl_products met korting eraf
                $invoice->total_excl_prods -= $korting;
                $invoice->total_excl -= $korting;

                if ($btw_tarief != 0) {
                  $invoice->vat -= round($korting * ($btw_tarief / 100), 2);
                }
                $invoice->total = $invoice->total_excl + $invoice->vat;
                break;
              case "amount":
                //amount ex btw
                $btw_tarief = $invoice->determineVat();
                $discount_amount_excl = round($_SESSION['discount']->amount / (($btw_tarief / 100) + 1), 2);

                $invoice->total_excl_prods -= $discount_amount_excl;
                $invoice->total_excl -= $discount_amount_excl;

                if ($btw_tarief != 0) {
                  $invoice->vat -= round($discount_amount_excl * ($btw_tarief / 100), 2);
                }
                $invoice->total = $invoice->total_excl + $invoice->vat;
                break;
            }
          }

          $invoice->save();

          InvoiceStatus::setStatus($invoice);

          $invoice->betreft = __("Bestelling via") . " " . Context::getSiteDomain(false);
          $invoice->save();
          $lorder->invoice_id_part = $invoice->id;

          $lorder->save();

          foreach ($invoice->invoice_products as $prod) {
            $prod->invoice_id = $invoice->id;
            $prod->save();
          }

          $order_message->order_status = $lorder->status;
          $order_message->order_id = $lorder->id;
          $order_message->save($errors);

          $invoice->removeFromStock();

          $_SESSION['order_saved'] = $lorder;

          if ($invoice->paymentmethod == Payment::PAYMENT_INCASSO || $invoice->paymentmethod == Payment::PAYMENT_OVERMAKEN || $invoice->paymentmethod == Payment::PAYMENT_OVERMAKEN_PART || $invoice->paymentmethod == Payment::PAYMENT_REMBOURS) {
            //incasso: factuur new, order new, versturen email.
            MailsFactory::sendOrderEmail($lorder, $invoice->id, $_SESSION['userObject']);

            if (PROJECT == 'earthmineralen') {
              $invoice->invoiced(false); //earthmineralen direct een factuur aanmaken.
            }

            $this->clearBasketSession();

            ResponseHelper::redirect(reconstructQuery(['action']) . 'action=payfinished&invoiceid=' . $invoice->id);
          }
          elseif ($invoice->paymentmethod == Payment::PAYMENT_PAYPAL) {
            $paymentobj = $invoice->getPaymentObject();
            $paymentobj->handlePayment($lorder, $invoice);
          }
          elseif ($invoice->paymentmethod == Payment::PAYMENT_MOLLIE) {
            $paymentobj = $invoice->getPaymentObject();
            $paymentobj->handlePayment($lorder, $invoice, isset($order["paymentmethod_psp"]) ? $order["paymentmethod_psp"] : false);
          }
          elseif ($invoice->paymentmethod == Payment::PAYMENT_OMNIKASSA) {
            //omnikassa: invoice new, order pending, naar omnikassa schermen
            $payment_omnikassa = new PaymentOmnikassa();
            $payment_omnikassa->handlePayment($lorder, $invoice);
          }
        }
      }

      $this->order = $order;
      $this->invoice = $invoice;
      $this->order_message = $order_message;

      $this->errors = $errors;
      BreadCrumbs::getInstance()->addItem('Bevestigen');
      $this->seo_title = "Besteloverzicht bevestigen";

      if (Config::isTrue("IS_RESPONSIVE")) {
        $this->template = 'pay3RespSuccess.php';
      }
    }

    public function executePayfinished() {

      $succesfullresult = false;
      $this->succesfullresult = $succesfullresult;
      $invoice = false;

      if (!empty($_GET['order_id']) && !empty($_GET['status']) && !empty($_GET['signature'])) {//omnikassa

        $order_id = $_GET['order_id'];
        $status = $_GET['status'];
        $signature = $_GET['signature'];

        $payment_omnikassa = new PaymentOmnikassa();

        $validate_result = $payment_omnikassa->validateFinishedPayment($order_id, $status, $signature);

        if ($validate_result === false) {
          logToFile('order', 'executePayfinished: Seal returned from server not equal to hashed data. Postdata: ' . print_r($_POST, true));
          $this->message = "Er is iets mis gegaan met uw bestelling. Heeft u uw bestelling onterecht geannuleerd? Neem contact op met de webshop.";
        }
        else {
          // overrule with cleaned parameters
          $order_id = $validate_result['order_id'];
          $status = $validate_result['status'];

          $order = Orders::getOrderAndInvoice($order_id);
          $invoice = null;
          if ($order) {
            $invoice = $order->invoice;
          }
          if (!$invoice) {
            logToFile('order', 'executePayfinished: INVOICE NIET GEVONDEN postdata: ' . print_r($_POST, true));
            $this->message = "Betaling niet geslaagd. Bestelling is niet gevonden!";
          }
          elseif ($invoice->paymentmethod == Payment::PAYMENT_OMNIKASSA) {

            /*
            COMPLETED
            De betaling was succesvol.
            EXPIRED
            De consument heeft niet betaald binnen de daarvoor gestelde periode.
            IN_PROGRESS
            De betaling is nog niet afgerond. Dit kan voorkomen als gevolg van een storing of vertraging in het achterland van de betalingsverwerking. Dit is
            een mogelijke uitkomst van een iDEAL of credit card betaling.
            CANCELLED
            De consument koos ervoor om niet te betalen.
            */

            if ($status == 'COMPLETED') {
              logToFile("order", "executePayfinished: payment successful. invoice id: " . $invoice->getId());
              $this->message = __('BEDANKT_OMNIKASSA');
              //betaling geslaag, verwerking gebeurd in achtergrond
              $this->clearBasketSession();
              $succesfullresult = true;
            }
            elseif ($status == 'IN_PROGRESS') {
              logToFile("order", "executePayfinished: payment pending. invoice id: " . $invoice->getId());
              $this->message = __("BEDANKT_OMNIKASSA_PENDING"); //afwachting betaling
              $this->clearBasketSession();
            }
            else {
              $this->executePayerror();

              return;
            }
          }
        }
      }
      elseif (!empty($_GET['paymentId']) && !empty($_GET['PayerID'])) { //PayPal payment
        $payment = PaymentPaypal::doPayment($_GET['paymentId'], $_GET['PayerID']);

        if ($payment !== false) {
          $invoice = null;

          $transactions = $payment->getTransactions();
          $transaction = $transactions[0];

          $invoice = Invoice::find_by([], "WHERE MD5(id) = '" . escapeForDB($transaction->getInvoiceNumber()) . "'");
          if (!$invoice) {
            logToFile('order', 'executePayfinished: INVOICE NIET GEVONDEN getdata: ' . print_r($_GET, true) . " invoice id md5" . $transaction->getInvoiceNumber());
            $this->message = "Bestelling niet geslaagd. Bestelling is niet gevonden!";
          }
          elseif ($invoice->paymentmethod == Payment::PAYMENT_PAYPAL) {

            if ($payment->getState() == 'approved') {
              $this->message = __("BEDANKT_OVERMAKENPAYPAL");
              logToFile('order', 'executePayfinished: INVOICE betaling goedgekeurd. invoice id md5 ' . $transaction->getInvoiceNumber());

              $succesfullresult = true;
              //betaling geslaagd
              $this->clearBasketSession();
            }
          }
        }
        else {
          logToFile('order', 'executePayfinished: INVOICE NIET GEVONDEN postdata: ' . print_r($_GET, true));
          $this->message = "Bestelling niet geslaagd. Bestelling is niet gevonden!";
        }
      }
      elseif (isset($_GET['invoice_id'])) { //MOLLIE betaling
        $invoice = Invoice::find_by_id($_GET['invoice_id']);

        if (!$invoice) {
          $this->message = "Betaling niet geslaagd. Bestelling is niet gevonden!";
        }
        elseif ($invoice->paymentmethod == Payment::PAYMENT_MOLLIE) {
//					$paymentobj = $invoice->getPaymentObject();
//					$result = $paymentobj->validateResult($invoice);
          //$this->message = $result['message'];
          $this->message = __("BEDANKT_OVERMAKEN_DEFAULT");
          if ($invoice->status == Invoice::INVOICE_STATUS_PAYED) {
            //betaling succesvol
            $this->clearBasketSession();
            $succesfullresult = true;
          }
          else {
            // (nog)niet betaald via postback. Even ophalen van mollie, om betere melding te geven.
            $this->message = __("Uw betaling wordt verwerkt, ververs de pagina om de laatste status op te halen");
            if ($invoice->paymentmethod_id != "") {
              try {
                $mollie = PaymentMollie::getMollie();
                $payment = $mollie->payments->get($invoice->paymentmethod_id);
                if ($payment->isPaid()) {
                  //toch betaald!
                  $this->message = __("BEDANKT_OVERMAKEN_DEFAULT");
                  $succesfullresult = true;
                  $this->clearBasketSession();
                }
                elseif ($payment->isPending()) {
                  $this->message = __("Uw betaling is in verwerking, maar nog niet bevestigd.<br/>Zodra deze bevestigd is ontvangt u van ons bericht.<br/>");
                }
                elseif ($payment->isCanceled()) {
                  $this->message = __("U heeft uw betaling geannuleerd.<br/>Heeft u vragen over onze producten neem dan gerust contact op.");
                }
              }
              catch (Mollie\Api\Exceptions\ApiException $e) {
                logToFile('order', 'mollie Mollie_API_Exception:' . $e->getMessage());
              }
            }
            if (!$succesfullresult) {
              $this->executePayerror();
              return;
            }
          }

        }
      }
      elseif (isset($_GET['invoiceid'])) {
        $invoice = Invoice::find_by_id($_GET['invoiceid']);
        if ($invoice->paymentmethod == Payment::PAYMENT_INCASSO) {
          $this->message = __("BEDANKT_INCASSO");
          $succesfullresult = true;
        }
        elseif ($invoice->paymentmethod == Payment::PAYMENT_OVERMAKEN) {
          $this->message = __("BEDANKT_OVERMAKEN");
          $succesfullresult = true;
        }
        elseif ($invoice->paymentmethod == Payment::PAYMENT_OVERMAKEN_PART) {
          $this->message = __("BEDANKT_OVERMAKENPART");
          $succesfullresult = true;
        }
        elseif ($invoice->paymentmethod == Payment::PAYMENT_REMBOURS) {
          $this->message = __("BEDANKT_REMBOURS");
          $succesfullresult = true;
        }
        else {
          $this->message = "Onbekende betaalmethode";
        }
      }
      else {
        $this->message = "Onbekende bestelling.";
      }
      $this->seo_title = "Bestelling afgerond";
      if (Config::isTrue("IS_RESPONSIVE")) {
        $this->template = 'payfinishedRespSuccess.php';
      }
      $this->succesfullresult = $succesfullresult;

      if (Config::isdefined("GA_E-COM_TRACKING") && $invoice) {
        $invoice = Invoice::getInvoiceAndProductsById($invoice->id);
        if (Config::get("GA_E-COM_TRACKING") === "old") {
          $ga_tracking = [];
          $ga_tracking['trans'] = [
            'id'          => $invoice->id,
            'affiliation' => 'Webshop',
            'revenue'     => $invoice->total,
            'shipping'    => $invoice->shipping == "" ? 0 : $invoice->shipping,
            'tax'         => $invoice->vat,
          ];
          $ga_tracking['trans_items'] = [];
          foreach ($invoice->invoice_products as $prod) {
            $product = Product::getProductAndContent($prod->product_id);
            if ($product) {
              $ga_tracking['trans_items'][] = [
                'sku'      => $product->code,
                'name'     => $product->content->name,
                'price'    => round($prod->pieceprice, 2),
                'quantity' => $prod->size,
              ];
            }
          }
          $this->ga_tracking = $ga_tracking;
        }
        else { //new version
          $order = Orders::find_by_id($invoice->order_id);
          if ($order) {
            $this->gtag_tracking_json = (new InvoiceService($invoice))->getGtagEventPurchaseJson($invoice->invoice_products, $order->getOrderNr());
          }
        }
      }

    }

    public function executePayerror() {

      if (isset($_POST['Data'])) {//omnikassa
        $omni_payment = new PaymentOmnikassa();
        $omni_payment->loadConfig();

        $omni_response = new OmniKassaResponse($_POST);
        $omni_response->setSecretKey($omni_payment->getSecretKey());
        if (DEVELOPMENT) {
          $omni_response->enableTestMode();
        }
        $is_valid = false;
        try {
          $omni_response->validate();
          $is_valid = true;
        }
        catch (UnexpectedValueException $invalid) {
          logToFile('order', 'executePayerror: Seal returned from server not equal to hashed data. Postdata: ' . print_r($_POST, true));
          $this->message = "Er is iets mis gegaan met uw bestelling. Heeft u uw bestelling onterecht geannuleerd? Neem contact op met de webshop.";
        }

        if ($is_valid) {

          $order = Orders::getOrderAndInvoice($omni_response->getOrderId());
          $invoice = null;
          if ($order) {
            $invoice = $order->invoice;
          }
          if (!$invoice) {
            logToFile('order', 'executePayerror: INVOICE NIET GEVONDEN postdata: ' . print_r($_POST, true));
            $this->message = "Bestelling niet geslaagd. Bestelling is niet gevonden!";
          }
          elseif ($invoice->paymentmethod == Payment::PAYMENT_OMNIKASSA) {

            if (OmniKassaEvents::CANCELLED == $omni_response->getResponseCode()) {
              logToFile("order", "executePayerror: payment cancelled. invoice id: " . $invoice->getId());
              $this->message = __("Uw betaling is geannuleerd."); //betaling geannuleerd
            }
            elseif (
              OmniKassaEvents::EXPIRED == $omni_response->getResponseCode() ||
              OmniKassaEvents::FAILURE == $omni_response->getResponseCode() ||
              OmniKassaEvents::SERVER_UNREACHABLE == $omni_response->getResponseCode()
            ) {
              logToFile("order", "executePayerror: payment " . $omni_response->getResponseCode() . ". invoice id: " . $invoice->getId());
              $this->message = __("Uw betaling is mislukt, probeert u het op een later tijdstip nogmaals.");
            }
          }
        }
      }
      elseif (!empty($_GET['token'])) { //paypal
        $this->message = __("U heeft uw betaling geannuleerd.");
      }
      elseif (!isset($this->message) || $this->message == "") {
        $this->message = __("Er is iets mis gegaan met uw betaling. Probeert u het op een later stadium nogmaals, of neem contact met ons op.");
      }
      $this->seo_title = "Bestelling: fout";
      if (Config::isTrue("IS_RESPONSIVE")) {
        $this->template = 'payerrorRespSuccess.php';
      }
      else {
        $this->template = 'payerrorSuccess.php';
      }
    }

    public function add_discount_to_invoice($invoice) {
      $errors = [];

      if (isset($_SESSION['discount'])) {
        if ($_SESSION['discount']->void != 0) {
          $errors[] = "Deze actiecode is helaas niet meer geldig.";
        }
        if (strtotime($_SESSION['discount']->start_date) > strtotime(date('Y-m-d')) || strtotime($_SESSION['discount']->end_date) < strtotime(date('Y-m-d'))) {
          $errors[] = "Deze actiecode is helaas niet (meer) geldig.";
        }
        if ($_SESSION['discount']->max_usages > 0 && $_SESSION['discount']->used >= $_SESSION['discount']->max_usages) {
          $errors[] = "Deze actiecode is al te vaak gebruikt.";
        }
        if ($_SESSION['discount']->amount != "") {
          if ($_SESSION['discount']->amount > $invoice->total) {
            $errors[] = "De ingevoerde korting is groter dan het totaal bedrag. Gelieve de gehele waarde van de kortingscode te gebruiken.";
          }
        }

        if (!empty($_SESSION['discount']->minimal_spending) && $_SESSION['discount']->minimal_spending > 0) {
          if ($invoice->total < $_SESSION['discount']->minimal_spending) {
            $errors[] = "Deze actiecode geldt alleen bij een minimale besteding van " . StringHelper::asMoney($_SESSION['discount']->minimal_spending);
          }
        }
        //check of de combinatie type en discount type kloppen
        if (count($errors) == 0) {
          $discount_to_invoice_product = $_SESSION['discount']->createInvoiceProduct($invoice);
          $discount_to_invoice_product->save();

          $_SESSION['discount']->used++;
          $_SESSION['discount']->save();

          //$discount_to_invoice_product->total is negative, thus do +=
          $invoice->total += $discount_to_invoice_product->total;
          //unset discount
          // unset($_SESSION['discount']);
        }
        else {
          unset($_SESSION['discount']);
          ResponseHelper::redirect(reconstructQuery(['action']) . 'action=pay3');
        }
      }
      //pd($invoice);
      //exit;
      return $invoice;
    }

    public function executeImportexcel() {
      $errors = [];
      if (isset($_POST['go'])) {

        $data = trim($_POST['exceldata']);
        if ($data == "") {
          $errors[] = __("Tekstveld is leeg.");
        }
        else {
          $data = explode("\n", $data);
          $to_add = [];
          $tel = -1;
          foreach ($data as $line) {
            $tel++;
            $vals = explode("\t", $line);
            if (count($vals) < 2) {
              $errors[] = __("Ongeldige gegevens geplakt in tekstveld. Er dienen 2 kolommen te zijn, gescheiden met een tab.");
              break;
            }
            $code = trim($vals[0]);
            $size = trim($vals[1]);
            $product = Product::find_by(["code" => $code]);

            //validate
            if (!$product && $tel == 0)
              continue; //negeer eerste rij indien veldnamen
            if (!is_numeric($size)) {
              $errors[] = __("Artikel aantal ongeldig") . " " . __("op regel") . " " . ($tel + 1) . ': ' . $size;
              continue;
            }
            if (!$product) {
              $errors[] = __("Artikelnummer niet gevonden") . " " . __("op regel") . " " . ($tel + 1) . ': ' . $code;
              continue;
            }

            $to_add[$product->id] = $size;

          }
          //				pd($to_add);
          //				pd($errors);
          //				exit;
          if (count($errors) == 0) {
            if (count($to_add) == 0) {
              $errors[] = __("Geen artikelen gevonden.");
            }
            else {
              $_POST['size'] = $to_add;
              $this->addToBasket($_SESSION['basket'], true);
            }
          }
        }
      }
      $this->errors = $errors;
    }

    /**
     * Validates recaptcha
     */
    public function executeRecaptcha() {
      $recaptcha_config = Config::get("BASKET_ASK_CAPTCHA");
      $errors = [];
      if (isset($_POST['g-recaptcha-response'])) {
        $response = file_get_contents("https://www.google.com/recaptcha/api/siteverify?secret=" . $recaptcha_config["secret_key"] . "&response=" . $_POST['g-recaptcha-response'] . "&remoteip=" . IpHelper::getIpAdress());
        $obj = json_decode($response);
        if ($obj->success == true) {
          //passes test
          $_SESSION["BASKET_RECAPTCHA"] = true;
          ResponseHelper::redirect(PageMap::getUrl('M_BASKET'));
        }
        else {
          //error handling
          $errors[] = __("Helaas, u bent gedetecteerd als robot. Probeer het nogmaals.");
        }
      }

      $this->recaptcha_config = $recaptcha_config;
      $this->errors = $errors;
    }

    /**
     * Sorteren winkelmandje
     * @param $basket
     * @return mixed
     */
    public function sortBasket($basket) {
      if (empty($basket['products'])) return $basket;

      if (Config::get('SORT_BASKET_PRODUCTS_BY_CAT', true)) {
        // map the object ids to the array keys
        $new_array = [];
        foreach ($basket['products'] as $product_info) {
          $new_array[$product_info['product']->id] = $product_info;
        }
        $basket['products'] = $new_array;
        // reorder the products in the basket based on categories order
        $basket['products'] = Product::sortProductsAsCatTree($basket['products'], 'product');
      }
      else {
        // default: sort by product code alphabetical
        usort($basket['products'], "Product::sortByCodeBasket");
      }

      return $basket;

    }

    /**
     * Sorteren op overzicht pagina (pay3)
     * @param $basket
     * @return mixed
     */
    public function sortOverview($basket) {
      return $basket;
    }

    /**
     * Sorteren producten voor pdf/invoice (pay3)
     * Dit kan anders zijn als op het overzicht scherm bij pay3
     * @param $basket
     * @return mixed
     */
    public function sortOverviewInvoice($basket) {
      return $basket;
    }

    public function updateBasketInSession($basket) {

      $_SESSION['basket'] = $basket;
      // no products, remove basket data
      if (!isset($basket['products']) || count($basket['products']) == 0) {
        $this->clearBasketSession();
      }
      else {
        // store basket session data in datafile
        if (Config::get('STORE_BASKET_SESSION', true) && isset($_SESSION['userObject'])) {
          $data_file = new DataFile();
          $data_file->setDir(DIR_UPLOADS . 'basketdata/');
          $data_file->writeFile($_SESSION['userObject']->id . '.dat', serialize($_SESSION['basket']));
        }
      }
    }

    public function clearBasketSession() {
      unset($_SESSION['basket']);
      unset($_SESSION['order']);
      unset($_SESSION['order_saved']);
      unset($_SESSION['discount']);

      if (Config::get('STORE_BASKET_SESSION', true) && isset($_SESSION['userObject'])) {
        // remove basket data file
        $data_file = new DataFile();
        $data_file->setDir(DIR_UPLOADS . 'basketdata/');
        $data_file->removeFile($_SESSION['userObject']->id . '.dat');
      }
    }

    public function executeAddaddress() {
      if (Config::get('USER_ADDRESS_SHOWMAP', true)) {
        Context::addJavascript("https://maps.googleapis.com/maps/api/js?v=3.exp&libraries=places&key=" . LocationHelper::getGoogleMapsKey());
      }

      $errors = [];

      $address = new OrganisationAddress();
      $address->type = 'delivery';

      $address->organisation_id = $_SESSION['userObject']->organisation->id;
      $address->contactname = isset($_POST['contactname']) ? trim($_POST['contactname']) : '';
      $address->organisation_name = isset($_POST['organisationname']) ? trim($_POST['organisationname']) : '';

      if (Config::isTrue("ORGANISATION_ADDRESS_EMAIL")) {
        $address->email = isset($_POST['email']) ? trim($_POST['email']) : '';
      }

      $address->address = isset($_POST['address']) ? trim($_POST['address']) : '';
      $address->number = isset($_POST['number']) ? trim($_POST['number']) : '';
      $address->zip = isset($_POST['zip']) ? trim($_POST['zip']) : '';
      $address->city = isset($_POST['city']) ? trim($_POST['city']) : '';
      if (isset($_POST['extension'])) {
        $address->extension = trim($_POST['extension']);
      }
      $address->country = isset($_POST['country']) ? trim($_POST['country']) : '';
      if (Config::get('USER_ADDRESS_SHOWMAP', true)) {
        $address->lat = "";
        $address->lng = "";
      }
      if ($address->contactname == "") {
        $errors['contactname'] = __("Naam contactpersoon");
      }
      if ($address->address == "") {
        $errors['address'] = __('Straat');
      }
      if ($address->number == "") {
        $errors['number'] = __('Huisnummer');
      }
      if ($address->zip == "") {
        $errors['zip'] = __("Postcode");
      }
      if ($address->city == "") {
        $errors['city'] = __("Plaats");
      }
      if ($address->country == "") {
        $errors['country'] = __("Land");
      }
      if (Config::isTrue("ORGANISATION_ADDRESS_EMAIL")) {
        if ($address->email != "" && !ValidationHelper::isEmail($address->email)) {
          $errors['email'] = __("E-mailadres");
        }
      }

      if (count($errors) == 0) {
        if (Config::get('USER_ADDRESS_SHOWMAP', true) && $address->lat == "") { //probeer hier op te halen
          $address_latlng = LocationHelper::retrieveLatLngGoogle($address->zip, $address->address . " " . $address->number, $address->city, $address->getCountry());
          if (!isset($address_latlng['latitude']) || $address_latlng['latitude'] == '' ||
            !isset($address_latlng['longitude']) || $address_latlng['longitude'] == '') {
            $errors['geolatlng'] = __("Coordinaten voor dit adres niet gevonden. Controleer de adresgegevens");
          }
          else {
            $address->lat = $address_latlng['latitude'];
            $address->lng = $address_latlng['longitude'];
          }
        }

        if (count($errors) == 0) {
          $address->save();

          ResponseHelper::exitAsJson("OK");
        }
      }
      else {
        ResponseHelper::exitAsJson($errors);
      }

      ResponseHelper::exit();
    }

    public function executePasswordforgotten() {
      $errors = [];
      if (isset($_POST['forgot_email'])) {
        $errors = $this->sendPasswordForgotten();
      }
      $this->errors = $errors;
    }

    public function executePasswordreset() {

      if (!isset($_GET["hash"]) || !isset($_GET["id"])) {
        ResponseHelper::redirect("/");
      }
      $l_user = User::find_by_id($_GET["id"]);
      if (!$l_user) {
        ResponseHelper::redirectNotFound();
      }

      $up = UserProfile::getUPByUserIdAndCode($l_user->id, "pw-reset");
      $upInsertTS = UserProfile::getUPByUserIdAndCode($l_user->id, "pw-validuntilTS");
      if (!$up || !$upInsertTS) {
        ResponseHelper::redirectAlertMessage(__("U heeft geen wachtwoord reset aangevraagd, of u heeft uw wachtwoord reeds ingesteld. Vraag uw wachtwoord opnieuw op via de wachtwoord vergeten functionaliteit op de inloggen pagina."));
      }
      if ($up->value != $_GET["hash"]) {
        ResponseHelper::redirectAlertMessage(__("Zorg dat u de complete link uit uw e-mail kopieert naar de browser."));
      }
      if (strtotime("-35 MINUTES") > $upInsertTS->value) {
        ResponseHelper::redirectAlertMessage(__("Uw wachtwoord link is verlopen. Vraag opnieuw uw wachtwoord op met de wachtwoord vergeten functionaliteit op de inloggen pagina."));
      }

      $errors = [];
      if (isset($_POST["go_password"])) {
        $passwordValid = ValidationHelper::isPasswords($_POST['password1'], $_POST['password2']);
        if ($passwordValid !== true) {
          $errors['password1'] = $passwordValid;
          $errors['password2'] = true;
        }
        if (count($errors) == 0) {
          $l_user->password = $_POST["password1"];
          if (Config::isTrue("USER_PASSWORD_ENCRYPT")) {
            $l_user->password = User::encrypt($l_user->password);
          }
          $l_user->save();

          $up->destroy();
          $upInsertTS->destroy();

          MessageFlashCoordinator::addMessage(__("Uw wachtwoord is opgeslagen. U kunt nu inloggen."));
          ResponseHelper::redirect(reconstructQueryAdd());

        }
      }

      $this->template = "passwordresetSuccess.php";
      $this->template_wrapper_only = true;
      $this->errors = $errors;
    }

    /**
     * Load stored basket from file if available
     * @return bool true on success, false on failure/not available
     */
    public function loadStoredBasket() {

      if (!Config::get('STORE_BASKET_SESSION', true)) return false;

      //basket session data should not yet be set, user should be logged in
      if (!isset($_SESSION['userObject']) || isset($_SESSION['basket'])) return false;

      // get datafile
      $data_file = new DataFile();
      $data_file->setDir(DIR_UPLOADS . '/basketdata/');
      $basket_data_file = $data_file->getFileContent($_SESSION['userObject']->id . '.dat');
      if ($basket_data_file) {
        $basket_data_file = unserialize($basket_data_file);
      }
      if (ArrayHelper::hasData($basket_data_file)) {
        // set basket data in session
        $_SESSION['basket'] = $basket_data_file;
        return true;
      }
      return false;
    }


  }
