<table class="default_table">
  <tr class="dataTableHeadingRow">
    <td colspan="2">
      <div style="display: flex; justify-content: space-between;">
        <div><?php echo __("Bedrijfsgegevens");?></div>
        <div style="display: flex; align-items: center; gap: 5px;">
          <?php
            if(!empty($organ->id)):
              $insertUser = User::find_by_id($organ->insertUser);
              $info = "";
              if($insertUser):
                $info .= "Aangemaakt door: ".$insertUser->getNaam()."\n";
              endif;
              $info .= "Aangemaakt op: ".$organ->getInsertTS()."\n";
              $updateUser = User::find_by_id($organ->updateUser);
              if($updateUser):
                $info .= "Laatst aangepast door: ".$updateUser->getNaam()."\n";
              endif;
              $info .= "Laatst aangepast op: ".$organ->getUpdateTS()."\n";
              echo showInfoButton($info,"Informatie");
            endif;
          ?>
          <?php if(Privilege::hasRight("ORGANISATION_DELETE") && $organ->id!="" && $_SESSION['userObject']->organisation_id != $organ->id): ?>
              <?php echo BtnHelper::getRemove('?action=delete&id=' . $organ->id, __("Weet u zeker dat u deze organisatie compleet wilt verwijderen?<br/>LET OP: ook alle gebruikers behorende bij deze organisatie worden verwijderd.")) ?>
          <?php endif; ?>
        </div>
      </div>
    </td>
  </tr>
  <?php if(Config::isTrue("ORGAN_CUST_NR_ENABLED")): ?>
    <tr class="dataTableRow">
      <td class="head"><?php
          if($organ->type == Organisation::TYPE_JONGERE):
            echo __('Personeelsnummer');
          else:
            echo __('Klantnummer');
          endif;
        ?></td>
      <td><input type="text" name="cust_nr" class="<?php echo hasError($errors, 'cust_nr'); ?>"  value="<?php echo escapeForInput($organ->cust_nr) ?>" style="width: 150px;"/></td>
    </tr>
  <?php endif; ?>

  <tr class="dataTableRow">
    <td class="head" style="width: 320px;"><?php echo __('Actief') ?></td>
    <td>
      <label>
        <?php if($_SESSION['userObject']->organisation_id == $organ->id): //je gaat niet je eigen organisatie inactief zetten ?>
          <input type="checkbox" name="organ_active" value="1" disabled="disabled" <?php writeIfCheckedVal($organ->active,1); ?> size="40"/>
          <input type="hidden" name="organ_active" value="<?php echo $organ->active; ?>" />
        <?php else : ?>
          <input type="checkbox" name="organ_active" value="1" <?php writeIfCheckedVal($organ->active,1); ?> size="40"/>
        <?php endif; ?>
        <?php echo __('Actief') ?>
      </label>
      <?php echo showHelpButton(__("Dit houdt in dat de organisatie actief deelneemt. Actieve organisaties zijn terug te zien in de bedrijvenlijst."), __("Actief")); ?>
    </td>
  </tr>

  <?php if(Config::isTrue("ORGANISATION_PROSPECT_ENABLED")): ?>
    <tr class="dataTableRow">
      <td class="head"><label for="is_prospect"><?php echo __('Prospect'); ?></label></td>
      <td>
        <label>
          <input type="checkbox" name="is_prospect" id="is_prospect" value="1" <?php writeIfCheckedVal($organ->is_prospect,1); ?> size="40"/>
          <?php echo __('Prospect') ?>
        </label>
      </td>
    </tr>
  <?php endif; ?>

  <?php if(Config::isTrue("USE_ORGAN_IS_CUST_WEBSHOP")) : ?>
    <tr class="dataTableRow"><td class="head"><?php echo __('Webshop klant'); ?></td>
      <td>
        <label>
          <input type="checkbox" name="is_webshop_cust" value="1" <?php writeIfCheckedVal($organ->is_webshop_cust,1); ?> size="40" />
          <?php echo __('Webshop klant') ?>
        </label>
      </td>
    </tr>
  <?php endif; ?>

  <tr class="dataTableRow">
    <td class="head"><?php echo __('Type'); ?></td>
    <td>
      <?php if(!isset($organisation_type_force) && count(Organisation::getTypes($_SESSION['userObject']->usergroup))!=1 && ($_SESSION['userObject']->organisation_id != $organ->id || $_SESSION['userObject']->usergroup == User::USERGROUP_SUPERADMIN)) : ?>
        <select id="type" name="type" class="<?php echo hasError($errors, 'type'); ?>" >
          <option value=""><?php echo __('Selecteer een bedrijfs-type'); ?></option>
          <?php
            $out = "";
            foreach(Organisation::getTypes($_SESSION['userObject']->usergroup) as $key=>$otypename) {
              $out .= '<option value="'.$key.'"';
              if($key==$organ->type) {
                $out .= ' selected ';
              }
              $out .= '>'.Organisation::getTypeDesc($key).'</option>';
            }
            echo $out;
          ?></select>
        <span class="asterisk">*</span>
        <?php echo showHelpButton("In sommige gevallen is het mogelijk om het bedrijfstype aan te passen. Dit kan echter invloed hebben op de mogelijke gebruikersgroepen. Let wel op dat u de gebruikersgroep van de personen controleerd. Dit kunt u doen door deze opnieuw op te slaan.","Bedrijfs type") ?>
      <?php else : ?>
        <input type="text" value="<?php echo Organisation::getTypeDesc($organ->type); ?>" disabled="disabled" />
        <input type="hidden" id="type" name="type" value="<?php echo $organ->type; ?>" />
      <?php endif; ?>
    </td>
  </tr>
  <tr class="dataTableRow" id="div_company_name">
    <td class="head"><?php echo __('Bedrijfsnaam') ?></td>
    <td><input type="text" name="name" class="<?php echo hasError($errors, 'name'); ?>"  value="<?php echo escapeForInput($organ->name) ?>" size="40" maxlength="150"/> <?php if( ($organ->id == "" || $organ->type!=Organisation::TYPE_PARTICULIER) && (!Config::isdefined('ORGANISATION_NAME_OBLIDGED') || Config::isTrue("ORGANISATION_NAME_OBLIDGED"))): ?><span class="asterisk">*</span><?php endif; ?></td>
  </tr>

  <?php if(isset($owners) && $organ->type != Organisation::TYPE_OTHER && (!Config::isdefined("DONT_USE_OWNER_USER_ID") || Config::isFalse("DONT_USE_OWNER_USER_ID"))): ?>
    <?php if(count($owners)>1): ?>
      <tr class="dataTableRow">
        <td class="head"><?php echo __('Account beheerder'); ?></td>
        <td>
          <select id="owner_user_id" name="owner_user_id"
                  class="<?php echo hasError($errors, 'owner_organisation_id'); ?>">
            <option value=""><?php echo __('Selecteer een account beheerder...'); ?></option>
            <?php
              $out = "";
              foreach ($owners as $owner) {
                $out .= '<option value="' . $owner->id . '"';
                if($owner->id == $organ->owner_user_id) {
                  $out .= ' selected ';
                }
                $out .= '>' . $owner->getNaamEnBedrijfsnaam() . " - " . User::getInternalUsergroupDesc($owner->usergroup) . '</option>';
              }
              echo $out;
            ?></select> <span class="asterisk">*</span>
          <input type="hidden" id="owner_organisation_id" name="owner_organisation_id" value=""/>
        </td>
      </tr>
    <?php else: //er is maar 1 eigenaar, dan altijd zichzelf.
      $firstowner = reset($owners);
      ?>
      <input type="hidden" id="owner_user_id" name="owner_user_id" value="<?php echo $firstowner->id ?>"/>
      <input type="hidden" id="owner_organisation_id" name="owner_organisation_id" value="<?php echo $firstowner->organisation_id ?>"/>
    <?php endif; ?>
  <?php endif; ?>

  <tr class="dataTableRow">
    <td class="head"><?php echo __('Bezoekadres'); ?></td>
    <td>
      <input type="text" name="address" id="address" class="address_street <?php echo hasError($errors, 'address'); ?>"  value="<?php echo displayAsHtml($organ->address) ?>" placeholder="<?php echo __("Straat"); ?>..." maxlength="70" />
      <input type="text" name="number" id="number" class="address_nr <?php echo hasError($errors, 'number'); ?>"  value="<?php echo displayAsHtml($organ->number) ?>" placeholder="<?php echo __("Huisnummer"); ?>..." maxlength="10"/>
      <span class="asterisk">*</span>
      <?php echo __('(straat + huisnummer)'); ?>
    </td>
  </tr>
  <tr class="dataTableRow">
    <td class="head"></td>
    <td>
      <input type="text" name="zip" id="zip" class="address_zip <?php echo hasError($errors, 'zip'); ?>"  value="<?php echo displayAsHtml($organ->zip) ?>" placeholder="<?php echo __("Postcode"); ?>..." maxlength="10" />
      <input type="text" name="city" id="city" class="address_city <?php echo hasError($errors, 'city'); ?>"  value="<?php echo displayAsHtml($organ->city) ?>" placeholder="<?php echo __("Woonplaats"); ?>..." maxlength="50" />
      <span class="asterisk">*</span>
      <?php echo __('(postcode + plaats)'); ?>
    </td>
  </tr>
  <?php if(Config::isTrue("ORGAN_USE_PROVINCE")): ?>
    <tr class="dataTableRow">
      <td class="head"></td>
      <td>
        <?php echo Province::getProvinceSelect($organ) ?>
       <?php echo __('(provincie)'); ?>
      </td>
    </tr>
  <?php endif; ?>

  <?php if(Config::get("ORGANISATION_EDIT_SHOW_EXTENSION", true)): ?>
    <tr class="dataTableRow">
      <td class="head"></td>
      <td>
        <input type="text" name="extension" id="extension" value="<?php echo displayAsHtml($organ->extension) ?>" placeholder="<?php echo __("Extra adresregel / extensie") ?>..."/>
        <?php echo __("Extra adresregel / extensie") ?>
      </td>
    </tr>
  <?php endif; ?>
  <tr class="dataTableRow"><td class="head"></td>
    <td>
      <?php echo Country::getCountrySelect('country', $organ, hasError($errors, 'country')) ?>
      <span class="asterisk">*</span> <?php echo __('(land)'); ?>
    </td>
  </tr>
  <?php if(Config::get("ORGAN_EDIT_SHOW_MAP",true)): ?>
    <tr class="dataTableRow">
      <td class="head"><?php echo __('Coördinaten (Latitude, Longitude)'); ?></td>
      <td>
        <input type="text" name="lat" id="lat" value="<?php echo $organ->lat; ?>" style="width: 150px;" /> <span class="asterisk">*</span>
        <input type="text" name="lng" id="lng" value="<?php echo $organ->lng; ?>" style="width: 150px;" /> <span class="asterisk">*</span>
        <input type="button" id="get_coordinates" value="<?php echo __('Coördinaten ophalen');?>" />
        <input type="button" id="show_map" value="<?php echo __('Toon kaart');?>" />
        <br/>
        <div id="google_maps" style="width: 100%; height: 300px; display: none;margin-top: 10px;"></div>
        <input id="pac-input" type="text" placeholder="<?php echo __('Ga naar een locatie');?>..." style="display: none;" />
      </td>
    </tr>
  <?php endif; ?>

  <tr class="dataTableRow"><td class="head"><?php echo __('Taal'); ?></td><td>
      <select name="locale" class="<?php echo hasError($errors, 'language'); ?>">
        <option value=""><?php echo __('Selecteer taal...'); ?></option>
        <?php
          $out = "";
          $locales['nl']='Nederlands';
          if(Config::isdefined('ORGANISATION_LANGUAGES')) {
            $locales = Config::get('ORGANISATION_LANGUAGES');
          }
          foreach($locales as $key=>$locale) {
            $out .= '<option value="'.$key.'"';
            if($key==$organ->language) {
              $out .= ' selected ';
            }
            $out .= '>'.$locale.'</option>';
          }
          echo $out;
        ?></select> <span class="asterisk">*</span>
    </td></tr>
  <tr class="dataTableRow">
    <td class="head"><?php echo __('E-mailadres algemeen') ?></td>
    <td><input type="text" name="organ_email" class="<?php echo hasError($errors, 'organ_email'); ?>"
               value="<?php echo displayAsHtml($organ->email) ?>" size="40"
               maxlength="255"/> <?php if(!Config::isdefined("ORGANISATION_EMAIL_OBLIDGED") || Config::isTrue("ORGANISATION_EMAIL_OBLIDGED")): ?>
        <span class="asterisk">*</span><?php endif; ?></td>
  </tr>
  <tr class="dataTableRow">
    <td class="head"><?php echo __('Website') ?></td>
    <td>
      <input type="text" name="website" class="<?php echo hasError($errors, 'website'); ?>" value="<?php echo displayAsHtml($organ->website) ?>" size="70"/> incl. https://
    </td>
  </tr>

  <tr class="dataTableRow">
    <td class="head"><?php echo __('Telefoonnummer') ?></td>
    <td>
      <input type="text" name="organ_phone" class="<?php echo hasError($errors, 'organ_phone'); ?>" value="<?php echo displayAsHtml($organ->phone) ?>" size="25" maxlength="25"/>
      <?php if(isset($oblidged) && in_array('organ_phone', $oblidged)): ?><label class="asterisk">*</label><?php endif ?>
      <?php echo BtnHelper::getPhone($organ->phone) ?>
    </td>
  </tr>
  <?php if(!Config::isdefined("ORGANISATION_SHOW_FAX") || Config::isTrue("ORGANISATION_SHOW_FAX")): ?>
    <tr class="dataTableRow">
      <td class="head"><?php echo __('Fax'); ?></td>
      <td>
        <input type="text" name="fax" value="<?php echo displayAsHtml($organ->fax) ?>" size="25" maxlength="25"/>
        <?php if(isset($oblidged) && in_array('fax', $oblidged)): ?><label class="asterisk">*</label><?php endif ?>
      </td>
    </tr>
  <?php endif; ?>
  <tr id="row_vat_number" class="dataTableRow">
    <td class="head"><?php echo __('BTW-nummer') ?></td>
    <td>
      <input type="text" name="vat_number" class="<?php echo hasError($errors, 'vat_number'); ?>" value="<?php echo displayAsHtml($organ->vat_number) ?>" size="40" maxlength="50"/>
      <?php writeOblidged('vat_number',$oblidged) ?>
      <a href="https://ec.europa.eu/taxation_customs/vies/?locale=nl" class="gsd-btn gsd-btn-tertiary" target="_blank"><?php echo __("Controleer BTW-nummer");?></a>
    </td>
  </tr>
  <tr id="row_coc_number" class="dataTableRow">
    <td class="head"><?php echo __('KvK-nummer') ?></td>
    <td><input type="text" name="coc_number" class="<?php echo hasError($errors, 'coc_number'); ?>"
               value="<?php echo displayAsHtml($organ->coc_number) ?>" size="40" maxlength="50"/>
      <?php writeOblidged('coc_number',$oblidged) ?>
      <a href="https://www.kvk.nl/zoeken/" class="gsd-btn gsd-btn-tertiary" target="_blank"><?php echo __("Controleer KvK-nummer");?></a>
    </td>
  </tr>
  <tr class="dataTableHeadingRow topborder">
    <td colspan="2"><?php echo __("Facturatie");?></td>
  </tr>
  <tr class="dataTableRow">
    <td class="head"><?php echo __('Factuuradres');?></td>
    <td>
      <label>
        <input type="checkbox" value="1" name="invoice_equal" id="invoice_equal" <?php writeIfCheckedVal($organ->invoice_equal,1)?>/>
        <?php echo __('Factuuradres is gelijk aan bezoekadres');?>
      </label>
    </td>
  </tr>
  <?php if(Config::isTrue("ORGANISATION_INVOICE_ADDRESS_NAME_ENABLED")): ?>
    <tr class="dataTableRow invoice_equal">
      <td class="head"></td>
      <td><input type="text" name="invoice_name" id="invoice_name" value="<?php echo displayAsHtml($organ->invoice_name) ?>" maxlength="255" placeholder="<?php echo __("Bedrijfsnaam"); ?>..." /></td>
    </tr>
  <?php endif; ?>
  <?php if(Config::isTrue("ORGANISATION_INVOICE_ADDRESS_CONTACT_NAME_ENABLED")): ?>
    <tr class="dataTableRow invoice_equal">
      <td class="head"></td>
      <td><input type="text" name="invoice_contact_name" id="invoice_contact_name" value="<?php echo displayAsHtml($organ->invoice_contact_name) ?>" maxlength="255" placeholder="<?php echo __("Contactpersoon"); ?>..." /></td>
    </tr>
  <?php endif; ?>
  <tr class="dataTableRow invoice_equal">
    <td class="head"></td>
    <td><input type="text" name="invoice_address" id="invoice_address" class="address_street <?php echo hasError($errors, 'invoice_address'); ?>" value="<?php echo displayAsHtml($organ->invoice_address) ?>" placeholder="<?php echo __("Straat"); ?>..." maxlength="50"/>
      <input type="text" name="invoice_number" id="invoice_number" class="address_nr <?php echo hasError($errors, 'invoice_number'); ?>" value="<?php echo displayAsHtml($organ->invoice_number) ?>" placeholder="<?php echo __("Huisnummer"); ?>..." maxlength="10"/>
      <span class="asterisk">*</span>
      <?php echo __('(straat + huisnummer)');?>
    </td>
  </tr>
  <tr class="dataTableRow invoice_equal">
    <td class="head"></td>
    <td>
      <input type="text" name="invoice_zip" id="invoice_zip" class="address_zip <?php echo hasError($errors, 'invoice_zip'); ?>" value="<?php echo displayAsHtml($organ->invoice_zip) ?>" placeholder="<?php echo __("Postcode"); ?>..." maxlength="10"/>
      <input type="text" name="invoice_city" id="invoice_city" class="address_city <?php echo hasError($errors, 'invoice_city'); ?>" value="<?php echo displayAsHtml($organ->invoice_city) ?>" placeholder="<?php echo __("Woonplaats"); ?>..." maxlength="50"/>
      <span class="asterisk">*</span>
      <?php echo __('(postcode + plaats)');?>
    </td>
  </tr>
  <?php if(Config::get("ORGANISATION_EDIT_SHOW_EXTENSION", true)): ?>
    <tr class="dataTableRow invoice_equal">
      <td class="head"></td>
      <td>
        <input type="text" name="invoice_extension" id="invoice_extension" value="<?php echo displayAsHtml($organ->invoice_extension) ?>" placeholder="<?php echo __("Extra adresregel / extensie") ?>..."/>
        <?php echo __("Extra adresregel / extensie") ?>
      </td>
    </tr>
  <?php endif; ?>
  <tr class="dataTableRow invoice_equal"><td class="head"></td>
    <td>
      <?php echo Country::getCountrySelect('invoice_country', $organ, hasError($errors, 'invoice_country')) ?>
      <span class="asterisk">*</span>
      <?php echo __('(land)'); ?>
    </td>
  </tr>
  <?php if(PROJECT != 'earthmineralen' && PROJECT != 'lavri') : ?>
    <tr class="dataTableRow">
      <td class="head"><?php echo __('Factuur emailadres'); ?></td>
      <td><input type="text" name="email_invoice" id="email_invoice" value="<?php echo $organ->email_invoice; ?>"
                 size="40" maxlength="255" placeholder="<?php echo __('Factuur emailadres'); ?>..."/>
        <?php if(isset($oblidged) && in_array('email_invoice',$oblidged)) : ?><label class="asterisk">*</label><?php endif ?>
      </td>
    </tr>
  <?php endif; ?>
  <?php if(Config::isTrue("USER_RECEIVES_INVOICE_ENABLED") && $organ->id != ""): ?>
    <tr class="dataTableRow">
      <td class="head"><?php echo __('Factuur gaat naar'); ?></td>
      <td><?php echo isset($default_invoice_user) && $default_invoice_user?'<span class="qtipa" title="U kunt de factuur persoon wijzigen onder &quot;Personen&quot; van deze organisatie.">' . $default_invoice_user->getNaam() . ($default_invoice_user->getEmail() != ""?" - " . $default_invoice_user->getEmail():'') . '</span>':'<span style="font-style: italic;">Nog geen persoon geselecteerd...</span>'; ?></td>
    </tr>
  <?php endif; ?>
  <?php if(Config::isTrue('ORGANISATION_OTHER_INVOICEUSER')):	?>
    <tr class="dataTableRow">
      <td class="head"><?php echo __('Factuur gaat naar'); ?></td>
      <td>
        <input type="text" name="invoice_user" id="invoice_user" value="<?php
          if($organ->invoice_userid!="") {
            $iu = User::getUserWithOrganById($organ->invoice_userid);
            if($iu) echo $iu->getNaamEnBedrijfsnaam();
          }
        ?>" />
        <input type="hidden" name="invoice_userid" id="invoice_userid" value="" />
      </td>
    </tr>
  <?php endif; ?>
  <?php if(Config::get("ORGANISATION_INTRACOMMUNAUTAIR",true)) : ?>
    <tr class="dataTableRow">
      <td class="head"><?php echo __('Intracommunautaire levering') ?></td>
      <td>
        <label>
          <input type="checkbox" name="no_vat" value="0" <?php writeIfCheckedVal($organ->no_vat, 1); ?> size="40"/>
          <?php echo __('Intracommunautaire levering') ?>
        </label>
        <?php echo showHelpButton(__("Als dit aangevinkt is hoeft het bedrijf geen btw te betalen."), __("Intracommunautaire levering")); ?>
      </td>
    </tr>
  <?php endif; ?>
  <tr id="row_nameof" class="dataTableRow">
    <td class="head"><?php echo __('Rekeningnummer t.n.v.') ?></td>
    <td><input type="text" name="nameof" class="<?php echo hasError($errors, 'nameof'); ?>" value="<?php echo escapeForInput($organ->nameof) ?>" size="40" maxlength="50"/>
      <?php if(isset($oblidged) && in_array('nameof',$oblidged)) : ?><label class="asterisk">*</label><?php endif ?>
    </td>
  </tr>
  <tr id="row_iban" class="dataTableRow">
    <td class="head"><?php echo __('IBAN');?></td><td>
      <input type="text" name="iban" id="iban" class="<?php echo hasError($errors, 'iban'); ?>" value="<?php echo displayAsHtml($organ->iban); ?>" size="40" maxlength="50"/>
      <span class="asterisk" id="asterisk_iban">*</span>
      <span id="ibanresult"></span> <input type="button" class="gsd-btn gsd-btn-tertiary" id="check_iban" value="<?php echo __("Controleer IBAN");?>" />
      <div style="display: none; color:red;" id="iban_helptext"><?php echo __('U mag alleen cijfers en (hoofd)letters invullen.');?></div>
    </td>
  </tr>
  <tr id="row_bic" class="dataTableRow">
    <td class="head"><?php echo __('BIC') ?></td>
    <td><input type="text" name="swift_bic" id="swift_bic" class="<?php echo hasError($errors, 'swift_bic'); ?>"  value="<?php echo displayAsHtml($organ->swift_bic); ?>" size="40" maxlength="50"/>
    </td>
  </tr>
  <?php if(PROJECT!='vdlcontainer' || Privilege::hasRight('ORGANISATION_PAYMENTTERM_EDIT')): ?>
    <tr class="dataTableRow">
      <td class="head"><?php echo __('Betalingstermijn'); ?></td>
      <td>
        <input type="text" name="paymentterm" value="<?php echo escapeForInput($organ->paymentterm) ?>" style="width: 60px;text-align: right;"/>
        <?php echo __('dagen');?>
        <?php echo showHelpButton("Hierbij wordt standaard ".Organisation::getDefaultPaymentterm()." dagen aangehouden. Een betalingstermijn van 0 dagen betekent dat deze klant vooruit dient te betalen.","Betalingstermijn") ?>
      </td>
    </tr>
  <?php endif; ?>
  <?php if(!Config::isdefined("ORGANISATION_INVOICE_TYPE_EDITABLE") || Config::isTrue("ORGANISATION_INVOICE_TYPE_EDITABLE")): ?>
    <?php $options = $organ->getOptions(); ?>
    <tr class="dataTableRow">
      <td class="head"><?php echo __("Factuur versturen via") ?></td>
      <td>
        <input type="hidden" name="options_helper" value="1" />
        <label class="kt-radio">
          <input type="radio" value="1" name="options[printinvoice]" id="options[printinvoice]" <?php if((!empty($options["printinvoice"]) && $options["printinvoice"]==1)) echo 'checked'?> style="vertical-align:middle;" />
          <?php echo __("Deze klant wil de facturen per post ontvangen"); ?>
          <span></span>
        </label>
        <br/>
        <label class="kt-radio">
          <input type="radio" value="1" name="options[emailinvoice]" id="options[emailinvoice]" <?php if(!empty($options["emailinvoice"]) && $options["emailinvoice"]==1 || !$options) echo 'checked' ?> style="vertical-align:middle;" />
          <?php echo __("Deze klant wil de facturen per email ontvangen"); ?>
          <span></span>
        </label>
      </td>
    </tr>
  <?php endif; ?>
  <?php if(Config::isdefined("ORGAN_EDIT_TOTAL_ORDER_DISCOUNT") && Config::get("ORGAN_EDIT_TOTAL_ORDER_DISCOUNT")) : ?>
    <tr class="dataTableRow">
      <td class="head" style="vertical-align: middle;"><?php echo __('% korting op totaalbedrag bestelling'); ?></td>
      <td style="vertical-align: middle;"><input type="text" name="order_discount_percentage" value="<?php echo escapeForInput($organ->order_discount_percentage); ?>" class="price" /> %</td>
    </tr>
  <?php endif; ?>
  <?php if(Privilege::hasRight('ORGANISATION_PAYMETHOD_EDIT') && Config::isdefined("ORGANISATION_SPECIFIC_PAYMETHODES")):
    $organspec = Config::get("ORGANISATION_SPECIFIC_PAYMETHODES");
    ?>
    <tr class="dataTableHeadingRow topborder"><td colspan="2">Betaalmethoden</td></tr>
    <?php if(isset($organspec['online']) && $organspec['online']): ?>
      <tr class="dataTableRow">
        <td class="head">Online afrekenen</td>
        <td>
          <label>
            <input type="checkbox" value="1" name="pay_online" id="pay_online" <?php writeIfCheckedVal($payments->pay_online,1)?> style="vertical-align:middle;"/>
            Klant mag online afrekenen
          </label>
          <?php echo showHelpButton("Online afrekenen is alleen mogelijk wanneer een payment service provider is gekoppeld aan uw account. Voor meer informatie neem contact op met GSD.","Online afrekenen")?>
        </td>
      </tr>
    <?php
    endif;
    if(isset($organspec['rekening']) && $organspec['rekening']):
      ?>
      <tr class="dataTableRow overmaken_rek">
        <td class="head">Overmaken op rekening (vooruit betalen)</td>
        <td>
          <label>
            <input type="checkbox" value="1" name="overmaken" id="overmaken" <?php writeIfCheckedVal($payments->overmaken,1)?> style="vertical-align:middle;"/>
            Klant mag overmaken op rekening (vooruit betalen)
          </label>
          <?php echo showHelpButton("De bestelling wordt pas verwerkt zodra het geld is overgemaakt.","Overmaken op rekening (vooruit betalen)")?>
        </td>
      </tr>
    <?php
    endif;
    if(isset($organspec['postpay']) && $organspec['postpay']):
      ?>
      <tr class="dataTableRow">
        <td class="head">Achteraf betalen (op rekening)</td>
        <td>
          <label>
            <input type="checkbox" value="1" name="postpay" id="postpay" <?php writeIfCheckedVal($payments->postpay, 1) ?>
                   style="vertical-align:middle;" />
            Klant mag overmaken op rekening (achteraf betalen)
          </label>
          <?php echo showHelpButton("De bestelling wordt direct verwerkt, het geld wordt later overgemaakt.","Achteraf betalen (op rekening)")?>
        </td>
      </tr>
    <?php
    endif;
    if(isset($organspec['incasso']) && $organspec['incasso']):
      ?>
      <tr class="dataTableRow">
        <td class="head">Automatisch incasso</td>
        <td>
          <label>
            <input type="checkbox" value="1" name="incasso" id="incasso" <?php writeIfCheckedVal($payments->incasso,1)?> style="vertical-align:middle;"/>
            Klant is akkoord met automatisch incasso
          </label>
        </td>
      </tr>
    <?php
    endif;
    if(isset($organspec['cash']) && $organspec['cash']):
      ?>
      <tr class="dataTableRow">
        <td class="head">Contant betalen</td>
        <td>
          <label>
            <input type="checkbox" value="1" name="cash" id="cash" <?php writeIfCheckedVal($payments->cash,1)?> style="vertical-align:middle;"/>
            Klant mag contant betalen
          </label>
        </td>
      </tr>
    <?php endif; ?>
  <?php endif; ?>
  <?php if(Privilege::hasRight('ORGANISATION_PDFTEMPLATE_EDIT') && ($organ->type==Organisation::TYPE_DISTRIBUTEUR || $organ->type==Organisation::TYPE_AGENT || $organ->type==Organisation::TYPE_OTHER)) : ?>
    <tr class="dataTableRow"><td class="head"><?php echo __('TEMPLATE') ?></td>
      <td>
        <?php
          echo $uploader->getInputs();
          if($organ->template!="") {
            echo ' <a href="'.$organ->getTemplateUrl().'?time='.time().'" target="_blank" title="' . __('Bekijk Factuur-/Briefpapier') . '"><img src="/images/pdf.png" /></a> ';
            echo __("verwijder template:") . '<input type="checkbox" value="1" name="template_delete">';
          }
          echo showHelpButton(__('TEMPLATE_DESC'), __('TEMPLATE'));
        ?>
      </td>
    </tr>
  <?php endif; ?>
  <?php if($organ->type == Organisation::TYPE_SHOP) : ?>
    <tr class="dataTableHeadingRow topborder"><td colspan="2"><?php echo __('Producten'); ?></td></tr>
    <tr class="dataTableRow">
      <td class="head"><label for="products_via_manual"><?php echo __('Mag producten handmatig toevoegen/wijzigen'); ?></label></td>
      <td>
        <label>
          <input type="checkbox" name="products_via_manual" id="products_via_manual" value="1" <?php writeIfCheckedVal($organ->products_via_manual, 1); ?> />
          Producten handmatig toevoegen wijzigen
        </label>
      </td>
    </tr>
  <?php endif; ?>
  <?php if($organ->type != Organisation::TYPE_PARTICULIER && (!Config::isdefined("ORGAN_EDIT_SHOW_SOCIAL_MEDIA") || Config::get("ORGAN_EDIT_SHOW_SOCIAL_MEDIA"))) :
    $socialurls = !Config::isdefined("ORGAN_EDIT_SHOW_SOCIAL_MEDIA") || Config::get("ORGAN_EDIT_SHOW_SOCIAL_MEDIA");
    if($socialurls===true) {
      $socialurls = OrganisationProfile::SOCIALPLATFORMS;
    }
    ?>
    <tr class="dataTableHeadingRow topborder"><td colspan="2">Social Media</td></tr>
    <?php foreach($socialurls as $surl => $name): ?>
      <tr class="dataTableRow"><td class="head"><?php echo $name ?> URL</td><td><input type="text" name="<?php echo $surl ?>_url" class="<?php echo hasError($errors, $surl.'_url'); ?>" value="<?php if(isset($organprofile[$surl])) echo displayAsHtml($organprofile[$surl]->value) ?>" /> <?php echo showHelpButton(__('Complete URL invullen inclusief https://'), ucfirst($surl)." URL"); ?></td></tr>
    <?php endforeach; ?>
  <?php endif; ?>

  <?php TemplateHelper::includePartial('_options.php', 'user', ['organ' => $organ]) ?>

  <?php if(Config::isdefined("ORGANISATION_PROFILE")): ?>
    <?php TemplateHelper::includePartial('_organprofile.php','user', ['organ' => $organ, 'organprofile' => $organprofile]) ?>
  <?php endif; ?>
  <?php if(Config::isTrue('ADMIN_UPLOAD_ORGAN_LOGO') && isset($uploader_logo)): ?>
    <tr class="dataTableRow">
      <td class="head"><?php echo __("Bedrijfslogo"); ?></td>
      <td>
        <?php
          echo $uploader_logo->getInputs();
          if($organ->logo_prev != "") {
            $out = "<Br/>";
            $out .= ' <a href="' . URL_UPLOADS . 'logo/' . $organ->logo_orgin . '?t=' . time() . '" id="logoimage" title="' . __("Bedrijfslogo") . '">';
            $out .= ' <img src="' . URL_UPLOADS . 'logo/' . $organ->logo_thumb . '?t=' . time() . '" alt="' . __("Bedrijfslogo") . '" style="border:1px solid #BBBBBB" /></a>';
            if(count($errors) > 0 && $organ->logo_orgin != ""):
              $out .= '<input type="hidden" name="logo_orgin" value="' . $organ->logo_orgin . '" />';
              $out .= '<input type="hidden" name="logo_prev" value="' . $organ->logo_prev . '" />';
              $out .= '<input type="hidden" name="logo_thumb" value="' . $organ->logo_thumb . '" />';
            endif;
            $out .= '<br/><label><input type="checkbox" value="1" name="logo_delete" /> '.__("verwijder logo") . '</label>';
            echo $out;
          }
          else {
            echo " - geen logo geüpload";
          }
        ?>
      </td>
    </tr>
    <?php endif; ?>
  <tr class="dataTableRow"><td class="head"><?php echo __('Interne opmerking') ?></td><td><textarea rows="4" name="intern_remark" class="form-control"><?php echo $organ->intern_remark ?></textarea></td></tr>

  <?php if(Privilege::hasRight('GLOBAL_ADMIN') && $organ->id!=null): ?>
    <tr class="dataTableRow">
      <td class="head">&nbsp;</td>
      <td>
        <a href="<?php echo reconstructQuery() ?>action=anonymizeorganisationdata" class="gsd-btn gsd-btn-tertiary gsd-confirm" data-gsd-title="<?php echo __('Bedrijfsgegevens anonimiseren') ?>"
           data-gsd-text="<?php echo __('Volgens de wet AVG heeft een relatie het recht om zijn gegevens te laten verwijderen. Is een bedrijf echter gekoppeld aan bijv. een factuur, dan kan deze niet verwijderd worden. Met deze knop blijft de relatie in het systeem staan, maar worden zijn bedrijfsgegevens geanonimiseerd.') ?>">
          <?php echo __('Bedrijfsgegevens anonimiseren') ?>
        </a> &nbsp;
        <?php echo showHelpButton("Volgens de wet AVG heeft een relatie het recht om zijn gegevens te laten verwijderen. Is een bedrijf echter gekoppeld aan bijv. een factuur, dan kan deze niet verwijderd worden. Met deze knop blijft de relatie in het systeem staan, maar worden zijn bedrijfsgegevens geanonimiseerd.", "Persoonsgegevens anonimiseren") ?>
      </td>
    </tr>
  <?php endif; ?>

</table>

<script type="text/javascript">
  <?php if(Config::isTrue("ORGAN_EDIT_SHOW_MAP")): ?>
  var map;
  var marker;
  var startup = true;

  function initmap() {
    var mapOptions = {
      center: new google.maps.LatLng(52.2, 5.5),
      zoom: 12,
      scrollwheel: false,
      mapTypeId: google.maps.MapTypeId.ROADMAP
    };

    map = new google.maps.Map(document.getElementById("google_maps"), mapOptions);

    <?php if($organ->lat != "" && $organ->lng != "") : ?>
    var latlng = new google.maps.LatLng(<?php echo $organ->lat; ?>, <?php echo $organ->lng; ?>);
    marker = new google.maps.Marker({
      position: latlng,
      map: map,
      optimized: false,
      draggable: true
    });

    map.setCenter(latlng);

    google.maps.event.addListener(marker, 'drag', function(id) {
      var m = marker.getPosition();
      $("#lat").val(m.lat());
      $("#lng").val(m.lng());
    });
    <?php endif; ?>

    var infowindow = new google.maps.InfoWindow();
    var input = document.getElementById('pac-input');
    map.controls[google.maps.ControlPosition.TOP_LEFT].push(input);
    var autocomplete = new google.maps.places.Autocomplete(input);
    autocomplete.bindTo('bounds', map);
    google.maps.event.addListener(autocomplete, 'place_changed', function() {
      infowindow.close();
      if(marker!=null) marker.setVisible(false);
      var place = autocomplete.getPlace();
      if (!place.geometry) {
        return;
      }

      // If the place has a geometry, then present it on a map.
      if (place.geometry.viewport) {
        map.fitBounds(place.geometry.viewport);
      } else {
        map.setCenter(place.geometry.location);
        map.setZoom(17);  // Why 17? Because it looks good.
      }

      if(marker==null) {
        marker     = new google.maps.Marker({
          position: place.geometry.location,
          map: map,
          optimized: false,
          draggable: true
        });

        var m = marker.getPosition();
        $("#lat").val(m.lat());
        $("#lng").val(m.lng());

        google.maps.event.addListener(marker, 'drag', function (id) {
          var m = marker.getPosition();
          $("#lat").val(m.lat());
          $("#lng").val(m.lng());
        });
      }

      marker.setIcon(/** @type {google.maps.Icon} */({
        url: place.icon,
        size: new google.maps.Size(71, 71),
        origin: new google.maps.Point(0, 0),
        anchor: new google.maps.Point(17, 34),
        scaledSize: new google.maps.Size(35, 35)
      }));
      marker.setVisible(true);

      var address = '';
      if (place.address_components) {
        address = [
          (place.address_components[0] && place.address_components[0].short_name || ''),
          (place.address_components[1] && place.address_components[1].short_name || ''),
          (place.address_components[2] && place.address_components[2].short_name || '')
        ].join(' ');
      }

      infowindow.setContent('<div><strong>' + place.name + '</strong><br/>' + address);
      infowindow.open(map, marker);
    });
  }
  <?php endif; ?>

  $(document).ready( function() {
    <?php if(Config::isTrue("ORGAN_EDIT_SHOW_MAP")): ?>
    initmap();
    $("#get_coordinates").on("click",  function(event) {

      var url = '<?php echo reconstructQueryAdd(['pageId']) ?>action=findlatlng';
      var street = trim($("#address").val());
      if(street!="") url += '&street='+street;
      var nr = trim($("#number").val());
      if(nr!="") url += '&nr='+nr;
      if($("#city").val()!="") url += '&city='+$("#city").val();
      if($("#country").val()!="") url += '&country='+$("#country").val();
      if($("#zip").val()!="") url += '&zipcode='+$("#zip").val().replace(/\s+/g, '');

      $.getJSON(url, function(data) {
        if(data==false) {
          swal("Kaart","We kunnen de locatie van dit adres op de kaart niet vinden. Controleer of u het adres wel kunt vinden met google maps, of versleep de marker handmatig op de juiste plaats.");
          //toon marker op locatie eigenaar
          var latLng = new google.maps.LatLng(<?php echo $_SESSION['userObject']->organisation->lat ?>,<?php echo $_SESSION['userObject']->organisation->lng ?>);
          if(!marker) {
            marker = new google.maps.Marker({
              position: latLng,
              map: map,
              optimized: false,
              draggable: true
            });
            google.maps.event.addListener(marker, 'drag', function(id) {
              var m = marker.getPosition();
              $("#lat").val(m.lat());
              $("#lng").val(m.lng());
            });
          }
          map.setCenter(latLng);
          marker.setPosition(latLng);
          $("#lat").val(data.latitude);
          $("#lng").val(data.longitude);
        }
        else {
          var latLng = new google.maps.LatLng(data.latitude,data.longitude);
          if(typeof latLng !== 'undefined') {
            if(!marker) {
              marker = new google.maps.Marker({
                position: latLng,
                map: map,
                optimized: false,
                draggable: true
              });
              google.maps.event.addListener(marker, 'drag', function(id) {
                var m = marker.getPosition();
                $("#lat").val(m.lat());
                $("#lng").val(m.lng());
              });
            }
            map.setCenter(latLng);
            marker.setPosition(latLng);
            $("#lat").val(data.latitude);
            $("#lng").val(data.longitude);
          }
        }
      });
    });

    $("#show_map").on("click",  function() {
      $("#google_maps, #pac-input").toggle();

      if($("#google_maps").is(":visible")) {
        var center = map.getCenter();
        google.maps.event.trigger(map, 'resize');
        map.setCenter(center);
        $(this).val("Verberg kaart");
      }
      else {
        $(this).val("Toon kaart");
      }

    });
    <?php endif; ?>

    <?php if(Config::isTrue('ORGAN_USE_PROVINCE')): ?>
    $("#country").on("change", function() {
      var country = $(this).val();
       $("select[name='province'] option").each(function() {
         if ($(this).data("country") == country || $(this).val() == "") {
           $(this).show();
         } else {
           $(this).hide();
         }
       });
     $("select[name='province']").val("");
    });
    <?php endif; ?>
  });

</script>