<?php

  namespace gsdfw\plugins\quotations\domain\updater;


  use Gsd\Updater\GsdUpdater;

  /**
   * Class QuotationsUpdater
   * Updaten van specifieke changes voor deze plugin
   * @package domain\updater
   */
  class QuotationsUpdater extends GsdUpdater {

    protected function execute13() {
      $this->executeQuery("ALTER TABLE `quotation` ADD `sales_chance` int(4) NULL AFTER `contract_period`; ");
      return true;
    }

    // Integrate prodcut options
    protected function execute12() {
      $this->executeQuery("ALTER TABLE `quotation_line` ADD `product_option` varchar(30) NULL AFTER `product_id`; ");
      $this->executeQuery("ALTER TABLE `quotation_blueprint_line` ADD `product_option` varchar(30) NULL AFTER `product_id`; ");
      return true;
    }

    protected function execute11() {
      $this->executeQuery("ALTER TABLE `quotation` ADD `pdf_template` varchar(20) NULL AFTER `version`; ");
      $this->executeQuery("ALTER TABLE `quotation` ADD `contract_period` int(6) NULL AFTER `valid_until`; ");

      $this->executeQuery("ALTER TABLE `quotation_blueprint` ADD `group` varchar(20) NULL AFTER `id`; ");
      $this->executeQuery("ALTER TABLE `quotation_blueprint` ADD `pdf_template` varchar(20) NULL AFTER `quotation_type`; ");
      $this->executeQuery("ALTER TABLE `quotation_blueprint` ADD `contract_period` varchar(20) NULL AFTER `description`; ");

      $this->executeQuery("ALTER TABLE `quotation_blueprint_line` ADD `discounted_on_price_percentage` decimal(6,4) NOT NULL DEFAULT 0 AFTER `size`; ");
      return true;
    }

    protected function execute10() {
      $this->executeQuery("ALTER TABLE `quotation` ADD `total_discount_exvat` DECIMAL (6,4) NULL AFTER `status`; ");
      return true;
    }

    protected function execute9() {
      $this->executeQuery("ALTER TABLE `quotation_line` ADD `discounted_on_price_ex_vat` DECIMAL (6,4) NULL AFTER `price_ex_vat`;");
      return true;
    }

    protected function execute8() {
      $this->executeQuery("ALTER TABLE `quotation_line` CHANGE `vatgroup` `vatgroup` INT(2) NOT NULL; ");
      $this->executeQuery("ALTER TABLE `quotation_added_cost` CHANGE `vat_percentage` `vat_percentage` INT(2) NOT NULL; ");
      return true;
    }

    protected function execute7() {
      $this->executeQuery("ALTER TABLE `quotation` ADD `description` TEXT NULL AFTER `subject`; ");
      return true;
    }

    /**
     * 04-07-2022 - Richard -
     * @return bool
     */
    protected function execute6() {
      $this->executeQuery("
        CREATE TABLE `quotation_message` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `quotation_id` mediumint(8) UNSIGNED NOT NULL,
  `quotation_status` varchar(20) NOT NULL DEFAULT 'new',
  `version` INT NOT NULL,
  `remind_cust` tinyint(1) NOT NULL DEFAULT '0',
  `message` text,
  `email_cust` tinyint(1) NOT NULL DEFAULT '0',
  `email_subject` varchar(255) DEFAULT NULL,
  `email_message` text,
  `insertTS` datetime DEFAULT NULL,
  `insertUser` mediumint(9) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ;  ");

      $this->executeQuery("
ALTER TABLE `quotation_message`
  ADD KEY `id` (`quotation_id`),
  ADD KEY `status` (`quotation_status`),
  ADD KEY `email_cust` (`email_cust`);
         ");

      return true;
    }

    /**
     * 04-07-2022 - Richard -
     * @return bool
     */
    protected function execute5() {
      return true;
    }


    /**
     * 04-07-2022 - Richard -
     * @return bool
     */
    protected function execute4() {
      $this->executeQuery("CREATE TABLE `quotation_blueprint` (
            `id` INT NOT NULL AUTO_INCREMENT ,
            `quotation_type` VARCHAR(50) NOT NULL ,
            `subject` VARCHAR(50) NOT NULL ,
            `description` TEXT NULL ,
            PRIMARY KEY (`id`),
            INDEX (`quotation_type`)) ;  ");

      return true;
    }


    /**
     * @return bool
     */
    protected function execute3() {
      $this->executeQuery("CREATE TABLE `quotation_blueprint_line` (
            `id` INT NOT NULL AUTO_INCREMENT ,  
            `quotation_blueprint_id` INT NOT NULL ,
            `description` VARCHAR(100) NULL , 
            `product_id` INT NOT NULL , 
            `size` INT NOT NULL , 
            PRIMARY KEY (`id`), 
            INDEX (`product_id`),
            INDEX (`quotation_blueprint_id`),
            INDEX (`size`) );  ");

      return true;
    }

    /**
     * @return bool
     */
    protected function execute2() {
      $this->executeQuery("CREATE TABLE `quotation_blueprint_added_costs` ( 
            `id` INT NOT NULL AUTO_INCREMENT ,  
            `description` VARCHAR(100) NOT NULL , 
            `price_ex_vat` DECIMAL(10,4) NOT NULL ,
            `vat_percentage` DECIMAL(10,2) NULL , 
            `quotation_blueprint_id` INT NOT NULL , 
            PRIMARY KEY (`id`), 
            INDEX (`description`), 
            INDEX (`quotation_blueprint_id`));  ");

      return true;
    }


    /**
     * 21-04-2022 - Richard - Initial
     * @return bool
     */
    protected function execute1() {
      $this->executeQuery("CREATE TABLE `quotation` (
         `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
         `user_id` mediumint(8) unsigned NULL,
         `organisation_id` mediumint(8) unsigned NOT NULL,
         `from_user_id` mediumint(8) unsigned NOT NULL,
         `from_organisation_id` mediumint(8) unsigned NOT NULL,
         `quotation_nr` varchar(20) DEFAULT NULL,
         `version` INT NOT NULL DEFAULT '1' ;
         `subject` varchar(255) DEFAULT NULL,
         `status` varchar(30) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'new',
         `total_prods_exvat` decimal(10,2) DEFAULT NULL,
         `total_exvat` decimal(10,2) DEFAULT NULL,
         `vat` decimal(10,2) NOT NULL DEFAULT '0.00',
         `total_inclvat` decimal(10,2) DEFAULT NULL,
         `quotationdate` date DEFAULT NULL,
         `valid_until` date NOT NULL,
         `void` tinyint(1) NOT NULL DEFAULT '0',
         `insertTS` datetime DEFAULT NULL,
         `updateTS` datetime DEFAULT NULL,
         PRIMARY KEY (`id`),
         INDEX (`version`)
        ) ");

      $this->executeQuery("CREATE TABLE `quotation_line` (
         `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
         `quotation_id` mediumint(8) unsigned NOT NULL,
         `product_id` mediumint(10) unsigned NOT NULL,
         `code` mediumint(8) unsigned NULL,
         `product_serial` integer(20) unsigned NULL,
         `description` text DEFAULT NULL,
         `size` smallint(8) unsigned NOT NULL,
         `type` varchar(30) NULL,
         `group` varchar(30) NULL,
         PRIMARY KEY (`id`)
        ) ");

      $this->executeQuery("CREATE TABLE `quotation_option` (
         `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
         `quotation_id` mediumint(8) unsigned NOT NULL,
         `type` varchar(30) NULL,
         `code` varchar(30) NULL,
         `value` varchar(30) NOT NULL, 
         PRIMARY KEY (`id`)
        ) ");

      $this->executeQuery("CREATE TABLE `quotation_added_cost` (
         `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
         `quotation_id` mediumint(8) unsigned NOT NULL,
         `group` varchar(30) NULL,
         `type` varchar(30) NULL,
         `code` varchar(30) NULL,
         `price_ex` DECIMAL(10,4) NOT NULL, 
         `vat_percentage` DECIMAL(10,4) NOT NULL, 
         `description` TEXT NOT NULL , 
         `amount` INT(10) NOT NULL DEFAULT '1',
         PRIMARY KEY (`id`)
        ) ");
      return true;
    }

    public function __construct() {
      $this->setVersionCode("quotations-version");
    }

  }